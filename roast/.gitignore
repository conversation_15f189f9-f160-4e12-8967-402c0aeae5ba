# Xcode and macOS
.DS_Store

# Xcode
build/
DerivedData/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata/

# Swift Package Manager
.build/
Packages/
Package.resolved

# CocoaPods
# Project config
roast/Config.xcconfig

Pods/
Podfile.lock

# Carthage
Carthage/Build/

# Fastlane
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots/
fastlane/test_output/

# Archives
*.xcarchive

# App Data
*.ipa
*.dSYM.zip
*.dSYM

# Playgrounds
timeline.xctimeline
playground.xcworkspace

# SwiftPM Xcode project
.xcodeproj/project.xcworkspace/xcshareddata/swiftpm/

# User-specific
*.swp
*.swo
*.xcuserstate

# Logs
*.log

# Other
*.orig
*.lock

# Ignore by default
# Uncomment if you use these tools
# .env
# .vscode/
# .idea/
