import UIKit
import UserNotifications

class AppDelegate: NSObject, UIApplicationDelegate, UNUserNotificationCenterDelegate {
    
    func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        // Set up notification delegate
        UNUserNotificationCenter.current().delegate = self
        
        // Check for notification launch
        if let notificationOption = launchOptions?[.remoteNotification] as? [AnyHashable: Any] {
            // App was launched from a push notification
            handleNotificationAtLaunch(notificationOption)
        }
        
        return true
    }
    
    // MARK: - Push Notification Registration
    
    func application(
        _ application: UIApplication,
        didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data
    ) {
        PushNotificationService.shared.setDeviceToken(deviceToken)
    }
    
    func application(
        _ application: UIApplication,
        didFailToRegisterForRemoteNotificationsWithError error: Error
    ) {
        PushNotificationService.shared.setDeviceTokenError(error)
    }
    
    // MARK: - Push Notification Handling
    
    func application(
        _ application: UIApplication,
        didReceiveRemoteNotification userInfo: [AnyHashable: Any],
        fetchCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void
    ) {
        print("[AppDelegate] Remote notification received in background")
        
        PushNotificationService.shared.handleNotificationReceived(userInfo)
        
        // Trigger data fetch
        AsyncProcessingService.shared.fetchReadyResults()
        
        completionHandler(.newData)
    }
    
    // MARK: - UNUserNotificationCenterDelegate
    
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        willPresent notification: UNNotification,
        withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void
    ) {
        print("[AppDelegate] Notification received while app in foreground")
        
        // Handle the notification
        PushNotificationService.shared.handleNotificationReceived(notification.request.content.userInfo)
        
        // Show notification even when app is in foreground
        completionHandler([.banner, .badge, .sound])
    }
    
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        didReceive response: UNNotificationResponse,
        withCompletionHandler completionHandler: @escaping () -> Void
    ) {
        print("[AppDelegate] User tapped notification")
        
        // Handle notification tap
        PushNotificationService.shared.handleNotificationReceived(response.notification.request.content.userInfo)
        
        // Navigate to ingredients view
        NotificationCenter.default.post(name: NSNotification.Name("NavigateToIngredients"), object: nil)
        
        completionHandler()
    }
    
    // MARK: - App Lifecycle
    
    func applicationDidBecomeActive(_ application: UIApplication) {
        // Clear badge when app becomes active
        PushNotificationService.shared.clearBadge()
        
        // Check for any ready results
        AsyncProcessingService.shared.fetchReadyResults()
        
        // Clean up expired requests
        AsyncProcessingService.shared.clearExpiredRequests()
    }
    
    func applicationDidEnterBackground(_ application: UIApplication) {
        // Enable background app refresh for notifications
        // This is configured in app settings, but we can request it
    }
    
    // MARK: - Notification Launch Handling
    
    private func handleNotificationAtLaunch(_ userInfo: [AnyHashable: Any]) {
        print("[AppDelegate] App launched from notification")
        
        PushNotificationService.shared.handleNotificationReceived(userInfo)
        
        // Delay to ensure services are initialized
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            AsyncProcessingService.shared.fetchReadyResults()
        }
    }
}
