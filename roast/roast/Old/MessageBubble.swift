import SwiftUI

// MARK: - Message Bubble
struct MessageBubble: View {
    @Binding var text: String
    var body: some View {
        HStack {
            ZStack(alignment: .topLeading) {
                if text.isEmpty {
                    if #available(iOS 16.0, *) {
                        Text("what's cooking?")
                            .foregroundColor(.secondary)
                            .padding(.vertical, 12)
                            .padding(.horizontal, 14)
                            .font(.system(.headline, design: .rounded, weight: .medium))
                            .opacity(0.85)
                    } else {
                        // Fallback on earlier versions
                        Text("what's cooking?")
                            .foregroundColor(.secondary)
                            .padding(.vertical, 12)
                            .padding(.horizontal, 14)
                            .font(Font.headline.bold())
                            .opacity(0.85)
                    }
                }
                TransparentTextEditor(text: $text, font: UIFont.preferredFont(forTextStyle: .headline), textColor: .secondaryLabel)
                    .frame(minHeight: 44, maxHeight: 63)
            }
        }
        .gesture(
            DragGesture(minimumDistance: 20, coordinateSpace: .local)
                .onEnded { value in
                    if abs(value.translation.height) > abs(value.translation.width) && value.translation.height > 0 {
                        UIApplication.shared.endEditing()
                    }
                }
        )
        .background(
            RoundedRectangle(cornerRadius: 8, style: .continuous)
                .fill(.ultraThinMaterial)
                // .strokeBorder(.secondary, lineWidth: 0.5)
        )
    }
}