# Asynchronous Ingredient Processing Implementation

## Architecture Overview

This implementation provides asynchronous ingredient processing with push notifications using a boolean toggle pattern. Users can submit images, receive immediate confirmation, and get notified when processing completes.

## Core Components

### 1. Models (`roast/Models/AsyncRequest.swift`)
- **AsyncRequest**: Tracks processing requests with ID, timestamp, and status
- **AsyncSubmissionResponse**: Server response with request_id
- **AsyncResultResponse**: Results containing ingredients array
- **PushTokenRequest**: Push token registration payload

### 2. Push Notification Service (`roast/Services/PushNotificationService.swift`)
- **Authorization Management**: Requests and tracks notification permissions
- **Device Token Handling**: Registers APNs device tokens with server
- **Notification Processing**: Handles incoming push notifications
- **Badge Management**: Updates app icon badges

### 3. Async Processing Service (`roast/Services/AsyncProcessingService.swift`)
- **Boolean Toggle State**: `@Published var hasDataReady = false`
- **Request Tracking**: Maintains pending and ready request lists
- **Persistent Storage**: Survives app termination using UserDefaults
- **Reactive Updates**: Uses Combine publishers for UI reactivity
- **Data Retrieval**: Automatically fetches results when ready

### 4. App Delegate (`roast/AppDelegate.swift`)
- **Push Notification Delegates**: Handles all notification lifecycle events
- **Background Processing**: Works when app is backgrounded/terminated
- **Token Registration**: Manages APNs device token registration

### 5. UI Integration
- **CameraView**: Boolean toggle between sync and async processing
- **Settings View**: User control over processing mode and notifications
- **Processing Indicators**: Visual feedback for pending requests
- **Ready Results Badge**: Notification badge when data is available

## Usage Flow

### Async Processing Mode (Default)
1. User captures images and taps "Submit for Processing"
2. Images are sent to `/api/ingredients` endpoint
3. Server returns `{"request_id": "uuid"}` immediately
4. App stores request_id and shows "Images Submitted" confirmation
5. User can continue using the app or close it
6. Server processes images and sends push notification with request_id
7. App receives notification and sets `hasDataReady = true`
8. App automatically fetches results from `/api/ingredients/{request_id}`
9. Results are displayed and user is navigated to ingredients view

### Sync Processing Mode
1. User captures images and taps "Generate List"
2. App shows loading screen and waits for full processing
3. Results returned immediately after processing completes
4. Same as original synchronous behavior

## Key Features

### Boolean Toggle Pattern
- `@AppStorage("useAsyncProcessing") private var useAsyncProcessing = true`
- Seamless switching between async and sync modes
- User preference persists across app sessions

### Reactive State Management
- `@Published var hasDataReady: Bool` triggers UI updates
- `@Published var pendingRequests: [AsyncRequest]` tracks active requests
- Combine publishers provide reactive data flow

### Persistent State
- Request tracking survives app termination
- Ready request IDs persist using UserDefaults
- Expired requests (>1 hour) automatically cleaned up

### Push Notification Integration
- APNs configured for development environment
- Device token automatically registered with server
- Notifications work in foreground, background, and terminated states
- Badge counts show number of ready results

### Error Handling
- Network timeouts and failures gracefully handled
- Expired data (404) automatically removed from tracking
- Retry logic for failed retrievals
- Notification permission fallbacks

## API Endpoints

| Endpoint | Method | Purpose | Timing |
|----------|--------|---------|--------|
| `/session` | GET | Get session_id | App launch |
| `/session` | POST | Register push token | Token received |
| `/api/ingredients` | POST | Submit images | User action |
| `/api/ingredients/{request_id}` | GET | Retrieve results | Notification triggered |

## Configuration

### Push Notifications
- APNs environment: `development` (in roast.entitlements)
- Required capability: Push Notifications
- Background modes: Background App Refresh

### Boolean Toggle
- Location: Settings > Processing Mode
- Default: Asynchronous Processing enabled
- Requires push notifications for async mode

## Testing

### Simulation Mode
- Settings view includes "Simulate Notification" button
- Creates test request and simulates notification after 3 seconds
- Useful for testing without actual server processing

### Debug Information
- Device token display in settings
- Request count tracking
- Comprehensive logging throughout system

## Benefits

1. **Improved UX**: Users don't wait for processing completion
2. **App Responsiveness**: Continue using app during processing
3. **Server Efficiency**: No polling required, push-driven notifications
4. **Battery Life**: No continuous network polling
5. **Reliability**: Persistent state survives app crashes/termination
6. **Flexibility**: Easy toggle between sync and async modes
