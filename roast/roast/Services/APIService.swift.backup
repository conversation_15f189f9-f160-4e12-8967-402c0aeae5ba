import Foundation

enum APIError: Error, LocalizedError {
	case invalidURL
	case requestFailed(Error)
	case noData
	case decodingError(Error)
	case encodingError(Error)
	case serverError(statusCode: Int, data: Data?, message: String? = nil)
	case noSessionId
	case unknown

	var errorDescription: String? {
		switch self {
		case .invalidURL: return "The URL provided was invalid."
		case .requestFailed(let error):
			return "The network request failed: \(error.localizedDescription)"
		case .noData: return "No data was received from the server."
		case .decodingError(let error):
			return "Failed to decode the server response: \(error.localizedDescription)"
		case .encodingError(let error):
			return "Failed to encode the request body: \(error.localizedDescription)"
		case .serverError(let statusCode, _, let message):
			return "Server error with status code \(statusCode). \(message ?? "")"
		case .noSessionId: return "No session ID available for the request. Please try again."
		case .unknown: return "An unknown error occurred."
		}
	}
}

class APIService {
	static let shared = APIService()
	private var serverBaseURL: String {
		SessionManager.shared.serverBaseURL
	}

	private init() {}

	func request<T: Decodable, U: Encodable>(
		endpoint: String,
		method: String,
		body: U?,
		timeoutInterval: TimeInterval = 60,  // default 1 minute
		completion: @escaping (Result<T, APIError>) -> Void
	) {
		// ensure session is valid before making a request
		SessionManager.shared.ensureSession { sessionId in
			guard let sessionId = sessionId, !sessionId.isEmpty else {
				print("[APIService] Error: No session ID available.")
				DispatchQueue.main.async { completion(.failure(.noSessionId)) }
				return
			}

			guard let url = URL(string: "\(self.serverBaseURL)\(endpoint)") else {
				print("[APIService] Error: Invalid URL for endpoint \(endpoint).")
				DispatchQueue.main.async { completion(.failure(.invalidURL)) }
				return
			}

			var request = URLRequest(url: url)
			request.httpMethod = method
			request.setValue("application/json", forHTTPHeaderField: "Content-Type")
			request.setValue(sessionId, forHTTPHeaderField: "x-session-id")
			request.timeoutInterval = timeoutInterval

			print("[APIService] Making \(method) request to \(url)")

			if let body = body {
				do {
					let encoder = JSONEncoder()
					request.httpBody = try encoder.encode(body)
					if let jsonString = String(data: request.httpBody!, encoding: .utf8) {
						print("[APIService] Request body: \(jsonString)")
					}
				} catch {
					print(
						"[APIService] Error: failed to encode request body for \(endpoint): \(error)"
					)
					DispatchQueue.main.async { completion(.failure(.encodingError(error))) }
					return
				}
			}

			URLSession.shared.dataTask(with: request) { [weak self] data, response, error in
				guard let self = self else { return }

				if let error = error {
					print(
						"[APIService] Error: Request failed for \(endpoint): \(error.localizedDescription)"
					)
					DispatchQueue.main.async { completion(.failure(.requestFailed(error))) }
					return
				}

				guard let httpResponse = response as? HTTPURLResponse else {
					print(
						"[APIService] Error: Invalid response (not HTTPURLResponse) for \(endpoint)."
					)
					DispatchQueue.main.async { completion(.failure(.unknown)) }
					return
				}

				print("[APIService] Response status \(httpResponse.statusCode) for \(endpoint)")

				guard (200...299).contains(httpResponse.statusCode) else {
					var errorMsg = "server error."
					if let data = data, let msg = String(data: data, encoding: .utf8) {
						errorMsg = msg
						print("[APIService] Error for \(endpoint): \(errorMsg)")
						if let requestBody = request.httpBody,
							let requestBodyString = String(data: requestBody, encoding: .utf8)
						{
							print("[APIService] Original request body: \(requestBodyString)")
						}
					}
					DispatchQueue.main.async {
						completion(
							.failure(
								.serverError(
									statusCode: httpResponse.statusCode, data: data,
									message: errorMsg)))
					}
					return
				}

				guard let data = data else {
					print("[APIService] Error: No data received for \(endpoint).")
					DispatchQueue.main.async { completion(.failure(.noData)) }
					return
				}

				do {
					let decoder = JSONDecoder()
					let decodedObject = try decoder.decode(T.self, from: data)
					DispatchQueue.main.async { completion(.success(decodedObject)) }
				} catch {
					print(
						"[APIService] Error: Decoding failed for \(endpoint): \(error.localizedDescription)"
					)
					if let responseString = String(data: data, encoding: .utf8) {
						print("[APIService] Failed to decode response: \(responseString)")
					}
					DispatchQueue.main.async { completion(.failure(.decodingError(error))) }
				}
			}.resume()
		}
	}

	struct EmptyBody: Encodable {}
	// helper for GET requests with no body
	func getRequest<T: Decodable>(
		endpoint: String,
		timeoutInterval: TimeInterval = 60,  // default 1 minute
		completion: @escaping (Result<T, APIError>) -> Void
	) {
		request(
			endpoint: endpoint, method: "GET", body: EmptyBody(), timeoutInterval: timeoutInterval,
			completion: completion)
	}

	// helper for POST requests
	func postRequest<T: Decodable, U: Encodable>(
		endpoint: String,
		body: U,
		timeoutInterval: TimeInterval = 60,  // default 1 minute
		completion: @escaping (Result<T, APIError>) -> Void
	) {
		request(
			endpoint: endpoint, method: "POST", body: body, timeoutInterval: timeoutInterval,
			completion: completion)
	}
}
