import SwiftUI

class LoadingStateManager: ObservableObject {
	@Published var isLoading = false
	@Published var loadingMessage = ""
	@Published var showToast = false
	@Published var toastMessage = ""
	@Published var toastType: ToastType = .info

	func showLoadingState(_ message: String) {
		loadingMessage = message
		isLoading = true
	}

	func hideLoadingState() {
		isLoading = false
		loadingMessage = ""
	}

	func showToast(_ message: String, type: ToastType = .info) {
		toastMessage = message
		toastType = type
		showToast = true
	}
}
