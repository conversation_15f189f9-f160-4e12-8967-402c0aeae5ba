import UIKit

class ImageProcessingService {
    static let shared = ImageProcessingService()
    
    private init() {}
    
    func generateThumbnail(from image: UIImage, maxDimension: CGFloat) -> UIImage? {
        let aspectRatio = image.size.width / image.size.height
        let size: CGSize
        
        if aspectRatio > 1 {
            size = CGSize(width: maxDimension, height: maxDimension / aspectRatio)
        } else {
            size = CGSize(width: maxDimension * aspectRatio, height: maxDimension)
        }
        
        UIGraphicsBeginImageContextWithOptions(size, false, 0.0)
        image.draw(in: CGRect(origin: .zero, size: size))
        let thumbnail = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        
        return thumbnail
    }
    
    func convertToBase64(images: [UIImage], compressionQuality: CGFloat = 0.9) -> [String] {
        return images.compactMap { image in
            guard let jpegData = image.jpegData(compressionQuality: compressionQuality) else { 
                return nil 
            }
            return jpegData.base64EncodedString()
        }
    }
    
    func exportImage(_ image: UIImage, compressionQuality: CGFloat = 0.9) -> URL? {
        guard let data = image.jpegData(compressionQuality: compressionQuality) else {
            return nil
        }
        
        let tempDir = FileManager.default.temporaryDirectory
        let url = tempDir.appendingPathComponent("export_\(UUID().uuidString).jpg")
        
        do {
            try data.write(to: url)
            return url
        } catch {
            print("[ImageProcessingService] Export failed: \(error)")
            return nil
        }
    }
}