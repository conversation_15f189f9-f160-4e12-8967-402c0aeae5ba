import Foundation

enum APIError: Error, LocalizedError {
	case invalidURL
	case requestFailed(Error)
	case noData
	case decodingError(Error)
	case encodingError(Error)
	case serverError(statusCode: Int, data: Data?, message: String? = nil)
	case noSessionId
	case sessionRefreshFailed
	case gatewayTimeout
	case unknown

	var errorDescription: String? {
		switch self {
		case .invalidURL: return "The URL provided was invalid."
		case .requestFailed(let error):
			return "The network request failed: \(error.localizedDescription)"
		case .noData: return "No data was received from the server."
		case .decodingError(let error):
			return "Failed to decode the server response: \(error.localizedDescription)"
		case .encodingError(let error):
			return "Failed to encode the request body: \(error.localizedDescription)"
		case .serverError(let statusCode, _, let message):
			return "Server error with status code \(statusCode). \(message ?? "")"
		case .noSessionId: return "No session ID available for the request. Please try again."
		case .sessionRefreshFailed: return "Session refresh failed. Please try again."
		case .gatewayTimeout: return "The server is taking longer than expected to process your request. Please try again with fewer images or wait a moment."
		case .unknown: return "An unknown error occurred."
		}
	}
}

// Structure to represent the specific error response for session invalidation
private struct SessionErrorResponse: Codable {
    let detail: String
}

class APIService {
	static let shared = APIService()
	private var serverBaseURL: String {
		SessionManager.shared.serverBaseURL
	}

	private init() {}

	func request<T: Decodable, U: Encodable>(
		endpoint: String,
		method: String,
		body: U?,
		timeoutInterval: TimeInterval = 60,
		isRetry: Bool = false, // Flag to prevent infinite retry loops
		completion: @escaping (Result<T, APIError>) -> Void
	) {
		// ensure session is valid before making a request
		SessionManager.shared.ensureSession { sessionId in
			guard let sessionId = sessionId, !sessionId.isEmpty else {
				print("[APIService] Error: No session ID available.")
				DispatchQueue.main.async { completion(.failure(.noSessionId)) }
				return
			}

			self.performRequest(
				endpoint: endpoint,
				method: method,
				body: body,
				sessionId: sessionId,
				timeoutInterval: timeoutInterval,
				isRetry: isRetry,
				completion: completion
			)
		}
	}

	private func performRequest<T: Decodable, U: Encodable>(
		endpoint: String,
		method: String,
		body: U?,
		sessionId: String,
		timeoutInterval: TimeInterval,
		isRetry: Bool,
		completion: @escaping (Result<T, APIError>) -> Void
	) {
		guard let url = URL(string: "\(serverBaseURL)\(endpoint)") else {
			print("[APIService] Error: Invalid URL for endpoint \(endpoint).")
			DispatchQueue.main.async { completion(.failure(.invalidURL)) }
			return
		}

		var request = URLRequest(url: url)
		request.httpMethod = method
		request.setValue("application/json", forHTTPHeaderField: "Content-Type")
		request.setValue(sessionId, forHTTPHeaderField: "x-session-id")
		request.timeoutInterval = timeoutInterval

		print("[APIService] Making \(method) request to \(url) (retry: \(isRetry), timeout: \(timeoutInterval)s)")

		if let body = body {
			do {
				let encoder = JSONEncoder()
				request.httpBody = try encoder.encode(body)
				if let jsonString = String(data: request.httpBody!, encoding: .utf8) {
					print("[APIService] Request body size: \(request.httpBody!.count) bytes")
				}
			} catch {
				print(
					"[APIService] Error: failed to encode request body for \(endpoint): \(error)"
				)
				DispatchQueue.main.async { completion(.failure(.encodingError(error))) }
				return
			}
		}

		URLSession.shared.dataTask(with: request) { [weak self] data, response, error in
			guard let self = self else { return }

			if let error = error {
				print(
					"[APIService] Error: Request failed for \(endpoint): \(error.localizedDescription)"
				)
				
				// Check if it's a timeout error
				if (error as NSError).code == NSURLErrorTimedOut {
					print("[APIService] Request timed out after \(timeoutInterval) seconds")
					DispatchQueue.main.async { 
						ToastManager.shared.showError(
							"Request Timeout",
							message: "The request is taking longer than expected. Try with fewer images or check your connection.",
							duration: 6.0
						)
						completion(.failure(.requestFailed(error)))
					}
				} else {
					DispatchQueue.main.async { completion(.failure(.requestFailed(error))) }
				}
				return
			}

			guard let httpResponse = response as? HTTPURLResponse else {
				print(
					"[APIService] Error: Invalid response (not HTTPURLResponse) for \(endpoint)."
				)
				DispatchQueue.main.async { completion(.failure(.unknown)) }
				return
			}

			print("[APIService] Response status \(httpResponse.statusCode) for \(endpoint)")

			// Handle successful responses
			guard !(200...299).contains(httpResponse.statusCode) else {
				guard let data = data else {
					print("[APIService] Error: No data received for \(endpoint).")
					DispatchQueue.main.async { completion(.failure(.noData)) }
					return
				}

				do {
					let decoder = JSONDecoder()
					let decodedObject = try decoder.decode(T.self, from: data)
					DispatchQueue.main.async { completion(.success(decodedObject)) }
				} catch {
					print(
						"[APIService] Error: Decoding failed for \(endpoint): \(error.localizedDescription)"
					)
					if let responseString = String(data: data, encoding: .utf8) {
						print("[APIService] Failed to decode response: \(responseString)")
					}
					DispatchQueue.main.async { completion(.failure(.decodingError(error))) }
				}
				return
			}

			// Handle error responses
			self.handleErrorResponse(
				status: httpResponse.statusCode,
				data: data,
				endpoint: endpoint,
				method: method,
				body: body,
				timeoutInterval: timeoutInterval,
				isRetry: isRetry,
				originalRequest: request,
				completion: completion
			)
		}.resume()
	}

	private func handleErrorResponse<T: Decodable, U: Encodable>(
		status: Int,
		data: Data?,
		endpoint: String,
		method: String,
		body: U?,
		timeoutInterval: TimeInterval,
		isRetry: Bool,
		originalRequest: URLRequest,
		completion: @escaping (Result<T, APIError>) -> Void
	) {
		var errorMsg = "server error."
		if let data = data, let msg = String(data: data, encoding: .utf8) {
			errorMsg = msg
			print("[APIService] Error for \(endpoint): \(errorMsg)")
			if let requestBody = originalRequest.httpBody,
				let requestBodyString = String(data: requestBody, encoding: .utf8)
			{
				print("[APIService] Original request body size: \(requestBody.count) bytes")
			}
		}

		switch status {
		case 401:
			self.handle401Error(
				data: data,
				endpoint: endpoint,
				method: method,
				body: body,
				timeoutInterval: timeoutInterval,
				isRetry: isRetry,
				completion: completion
			)
		case 429:
			self.handle429Error(data: data, completion: completion)
		case 504:
			self.handle504Error(endpoint: endpoint, completion: completion)
		default:
			DispatchQueue.main.async {
				completion(
					.failure(
						.serverError(
							statusCode: status, data: data, message: errorMsg)))
			}
		}
	}

	private func handle401Error<T: Decodable, U: Encodable>(
		data: Data?,
		endpoint: String,
		method: String,
		body: U?,
		timeoutInterval: TimeInterval,
		isRetry: Bool,
		completion: @escaping (Result<T, APIError>) -> Void
	) {
		print("[APIService] Handling 401 error for \(endpoint)")

		// Check if we can parse the response body for the specific session error
		guard let data = data else {
			print("[APIService] 401 error but no response data")
			DispatchQueue.main.async {
				completion(.failure(.serverError(statusCode: 401, data: data, message: "Unauthorized")))
			}
			return
		}

		do {
			let decoder = JSONDecoder()
			let errorResponse = try decoder.decode(SessionErrorResponse.self, from: data)
			
			// Check if this is the specific session invalidation error
			if errorResponse.detail == "Invalid or expired session ID." {
				print("[APIService] Detected invalid/expired session error. Attempting session refresh...")
				
				// Only attempt retry if this isn't already a retry
				if !isRetry {
					self.refreshSessionAndRetry(
						endpoint: endpoint,
						method: method,
						body: body,
						timeoutInterval: timeoutInterval,
						completion: completion
					)
				} else {
					print("[APIService] Already attempted retry for session refresh. Failing.")
					DispatchQueue.main.async {
						completion(.failure(.sessionRefreshFailed))
					}
				}
				return
			}
		} catch {
			print("[APIService] Could not parse 401 response as session error: \(error)")
		}

		// If we get here, it's a 401 but not the specific session error we're looking for
		DispatchQueue.main.async {
			completion(.failure(.serverError(statusCode: 401, data: data, message: "Unauthorized")))
		}
	}

	private func handle429Error<T: Decodable>(
		data: Data?,
		completion: @escaping (Result<T, APIError>) -> Void
	) {
		print("[APIService] Handling 429 Too Many Requests error")
		
		// Show toast notification for 429 error
		DispatchQueue.main.async {
			ToastManager.shared.showWarning(
				"Too Many Requests",
				message: "You're making requests too quickly. Please wait a moment and try again.",
				duration: 5.0
			)
			
			completion(.failure(.serverError(statusCode: 429, data: data, message: "Too Many Requests")))
		}
	}

	private func handle504Error<T: Decodable>(
		endpoint: String,
		completion: @escaping (Result<T, APIError>) -> Void
	) {
		print("[APIService] Handling 504 Gateway Timeout error for \(endpoint)")
		
		// Show specific toast for gateway timeout (common with image processing)
		DispatchQueue.main.async {
			if endpoint.contains("/api/ingredients") {
				ToastManager.shared.showError(
					"Processing Timeout",
					message: "Image processing is taking longer than expected. Try with fewer images or smaller file sizes.",
					duration: 8.0
				)
			} else {
				ToastManager.shared.showError(
					"Gateway Timeout",
					message: "The server is taking too long to respond. Please try again.",
					duration: 6.0
				)
			}
			
			completion(.failure(.gatewayTimeout))
		}
	}

	private func refreshSessionAndRetry<T: Decodable, U: Encodable>(
		endpoint: String,
		method: String,
		body: U?,
		timeoutInterval: TimeInterval,
		completion: @escaping (Result<T, APIError>) -> Void
	) {
		print("[APIService] Attempting to refresh session and retry request...")
		
		// Clear the current invalid session
		SessionManager.shared.clearSession()
		
		// Request a new session
		SessionManager.shared.ensureSession { newSessionId in
			guard let newSessionId = newSessionId, !newSessionId.isEmpty else {
				print("[APIService] Failed to obtain new session ID")
				DispatchQueue.main.async {
					completion(.failure(.sessionRefreshFailed))
				}
				return
			}
			
			print("[APIService] Successfully obtained new session ID, retrying original request...")
			
			// Retry the original request with the new session ID (marking as retry)
			self.performRequest(
				endpoint: endpoint,
				method: method,
				body: body,
				sessionId: newSessionId,
				timeoutInterval: timeoutInterval,
				isRetry: true, // Mark as retry to prevent infinite loops
				completion: completion
			)
		}
	}

	struct EmptyBody: Encodable {}
	
	// helper for GET requests with no body
	func getRequest<T: Decodable>(
		endpoint: String,
		timeoutInterval: TimeInterval = 60,
		completion: @escaping (Result<T, APIError>) -> Void
	) {
		request(
			endpoint: endpoint, method: "GET", body: EmptyBody(), 
			timeoutInterval: timeoutInterval, completion: completion)
	}

	// helper for POST requests
	func postRequest<T: Decodable, U: Encodable>(
		endpoint: String,
		body: U,
		timeoutInterval: TimeInterval = 60,
		completion: @escaping (Result<T, APIError>) -> Void
	) {
		request(
			endpoint: endpoint, method: "POST", body: body, 
			timeoutInterval: timeoutInterval, completion: completion)
	}
}
