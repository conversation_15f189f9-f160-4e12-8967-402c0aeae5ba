import UserNotifications
import UIKit
import Combine

class PushNotificationService: NSObject, ObservableObject {
    static let shared = PushNotificationService()
    
    @Published var isAuthorized = false
    @Published var deviceToken: String?
    
    private let apiService = APIService.shared
    
    override init() {
        super.init()
        checkAuthorizationStatus()
    }
    
    // MARK: - Authorization
    
    func requestAuthorization() async -> Bool {
        let center = UNUserNotificationCenter.current()

        do {
            let granted = try await center.requestAuthorization(options: [.alert, .badge, .sound])
            await MainActor.run {
                self.isAuthorized = granted
            }

            if granted {
                await requestDeviceToken()
            }

            return granted
        } catch {
            print("[PushNotificationService] Authorization error: \(error)")
            await MainActor.run {
                self.isAuthorized = false
            }
            return false
        }
    }
    
    private func checkAuthorizationStatus() {
        UNUserNotificationCenter.current().getNotificationSettings { [weak self] settings in
            DispatchQueue.main.async {
                self?.isAuthorized = settings.authorizationStatus == .authorized
            }
        }
    }
    
    // MARK: - Device Token Management
    
    @MainActor
    private func requestDeviceToken() async {
        UIApplication.shared.registerForRemoteNotifications()
    }
    
    func setDeviceToken(_ token: Data) {
        let tokenString = token.map { String(format: "%02.2hhx", $0) }.joined()

        DispatchQueue.main.async { [weak self] in
            self?.deviceToken = tokenString
        }

        print("[PushNotificationService] Device token received: \(tokenString.prefix(8))...")

        // Register token with server
        registerTokenWithServer(tokenString)
    }
    
    func setDeviceTokenError(_ error: Error) {
        print("[PushNotificationService] Device token error: \(error)")

        // Retry logic
        DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) { [weak self] in
            Task {
                await self?.requestDeviceToken()
            }
        }
    }
    
    // MARK: - Server Registration
    
    private func registerTokenWithServer(_ token: String) {
        let payload = PushTokenRequest(push_token: token)

        apiService.postRequest(
            endpoint: "/session",
            body: payload,
            timeoutInterval: 60,
            completion: { (result: Result<EmptyResponse, APIError>) in
                switch result {
                case .success:
                    print("[PushNotificationService] Token registered successfully")
                case .failure(let error):
                    print("[PushNotificationService] Token registration failed: \(error)")
                    // Retry after delay
                    DispatchQueue.main.asyncAfter(deadline: .now() + 10.0) { [weak self] in
                        self?.registerTokenWithServer(token)
                    }
                }
            }
        )
    }
    
    // MARK: - Notification Handling
    
    func handleNotificationReceived(_ userInfo: [AnyHashable: Any]) {
        print("[PushNotificationService] Notification received: \(userInfo)")
        
        // Extract request_id from push payload
        if let requestId = userInfo["request_id"] as? String {
            print("[PushNotificationService] Processing completion for request: \(requestId)")
            
            // Notify AsyncProcessingService
            AsyncProcessingService.shared.markRequestReady(requestId)
        }
    }
    
    // MARK: - Badge Management
    
    func updateBadge(count: Int) {
        DispatchQueue.main.async {
            UIApplication.shared.applicationIconBadgeNumber = count
        }
    }
    
    func clearBadge() {
        updateBadge(count: 0)
    }
}

// Empty response for token registration
struct EmptyResponse: Codable {}
