import Foundation

class InventoryParsingService {
    static let shared = InventoryParsingService()
    
    private init() {}
    
    struct ServerResponse: Decodable {
        let results: [ResultItem]
        
        struct ResultItem: Decodable {
            let ingredients_list: InventoryWrapper?
            let error: String?
            
            struct InventoryWrapper: Decodable {
                let inventory: [Ingredient]?
            }
        }
    }
    
    func parseInventory(from data: Data) -> [Ingredient]? {
        do {
            let decoded = try JSONDecoder().decode(ServerResponse.self, from: data)
            let allItems = decoded.results
                .compactMap { $0.ingredients_list?.inventory }
                .flatMap { $0 }
            return allItems.isEmpty ? nil : allItems
        } catch {
            print("[InventoryParsingService] Parse error: \(error)")
            return nil
        }
    }
}