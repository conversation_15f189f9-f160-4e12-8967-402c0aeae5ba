import AVFoundation
import Combine
import UIKit

class CameraManager: NSObject, ObservableObject, AVCapturePhotoCaptureDelegate {
    deinit { print("[CameraManager] deinit") }
    public let session = AVCaptureSession()
    private let photoOutput = AVCapturePhotoOutput()
    private let queue = DispatchQueue(label: "camera.frame.queue")
    private var photoCaptureCompletion: ((UIImage?) -> Void)?

    override init() {
        super.init()
        configure()
    }

    private var isConfigured = false
    private func configure() {
        // Print supported photo formats and quality flags
        // printSupportedPhotoFormats()
        // Only configure once
        guard !isConfigured else { return }
        isConfigured = true
        session.beginConfiguration()

        guard
            let device = AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: .back),
            let input = try? AVCaptureDeviceInput(device: device),
            session.canAddInput(input)
            else { return }
        
        print("Device: \(device.deviceType)")

        session.addInput(input)

        guard session.canAddOutput(photoOutput) else { return }
        session.addOutput(photoOutput)

        setCaptureFormat(fps: 15)

        session.commitConfiguration()
    }

    /// Selects the best format under max resolution and with at least minFPS supported
    /// Dynamically set the camera format and FPS for capture/preview quality
    private func setCaptureFormat(fps: Double, maxWidth: Int32? = nil, maxHeight: Int32? = nil) {
        // get device being used by session
        guard let device = session.inputs.compactMap({ $0 as? AVCaptureDeviceInput }).first?.device else {
            print("[CameraManager] No camera device found to configure format")
            return
        }
        // filter formats that support fps requests
        let formats = device.formats.filter { format in
            let maxFPS = format.videoSupportedFrameRateRanges.first?.maxFrameRate ?? 0
            if maxWidth != nil || maxHeight != nil {
                let dim = CMVideoFormatDescriptionGetDimensions(format.formatDescription)
                return maxWidth == nil || Int(dim.width) <= Int(maxWidth!) &&
                    maxHeight == nil || Int(dim.height) <= Int(maxHeight!) &&
                    maxFPS >= fps &&
                    format.isHighestPhotoQualitySupported
            }
            return maxFPS >= fps && format.isHighestPhotoQualitySupported
        }
        
        // sort formats by resolution (w x h), descending
        let sortedFormats = formats.sorted { f1, f2 in
            let dim1 = CMVideoFormatDescriptionGetDimensions(f1.formatDescription)
            let dim2 = CMVideoFormatDescriptionGetDimensions(f2.formatDescription)
            return (Int(dim1.width) * Int(dim1.height)) > (Int(dim2.width) * Int(dim2.height))
        }
        
        guard let bestFormat = sortedFormats.first else {
            print("[CameraManager] No suitable format found for \(fps) FPS")
            return
        }
        
        do {
            try device.lockForConfiguration()
            device.activeFormat = bestFormat
            let frameDuration = CMTimeMake(value: 1, timescale: Int32(fps))
            device.activeVideoMinFrameDuration = frameDuration
            device.activeVideoMaxFrameDuration = frameDuration
            device.unlockForConfiguration()
            let dims = CMVideoFormatDescriptionGetDimensions(bestFormat.formatDescription)
            print("[CameraManager] Set highest resolution format to \(dims.width)x\(dims.height) @ \(fps) FPS, highest quality: \(bestFormat.isHighestPhotoQualitySupported)")
        } catch {
            print("[CameraManager] Failed to set camera format: \(error.localizedDescription)")
        }
        session.sessionPreset = .inputPriority
    }

    /// Print all formats and their high quality support
    private func printSupportedPhotoFormats() {
        guard let device = AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: .back) else {
            print("[CameraManager] No camera device found for format debug print")
            return
        }
        var supportedMaxPhotoDimensions: [CMVideoDimensions] = []
        for format in device.formats {
            let desc = format.formatDescription
            let dims = CMVideoFormatDescriptionGetDimensions(desc)
            let isHighPhotoQualitySupported = format.isHighPhotoQualitySupported
            let isHighestPhotoQualitySupported = format.isHighestPhotoQualitySupported
            if isHighPhotoQualitySupported || isHighestPhotoQualitySupported {
                supportedMaxPhotoDimensions.append(dims)
            }
            let mediaType = CMFormatDescriptionGetMediaType(desc)
            let pixelFormatType = CMFormatDescriptionGetMediaSubType(desc)
            let formatDescString = "mediaType: \(mediaType), pixelFormat: 0x" + String(format: "%08X", pixelFormatType)
            print("[CameraManager] Format: \(dims.width)x\(dims.height), \(formatDescString), isHighPhotoQualitySupported: \(isHighPhotoQualitySupported), isHighestPhotoQualitySupported: \(isHighestPhotoQualitySupported)")
        }
        print("[CameraManager] supportedMaxPhotoDimensions: \(supportedMaxPhotoDimensions.map { "\($0.width)x\($0.height)" }.joined(separator: ", "))")
    }

    /// Only change FPS
    func setFPS(_ fps: Double) {
        guard let device = session.inputs.compactMap({ $0 as? AVCaptureDeviceInput }).first?.device else {
            print("[CameraManager] No camera device found to set FPS")
            return
        }
        
        let frameDuration = CMTimeMake(value: 1, timescale: Int32(fps))
        
        // check if FPS is already set
        if device.activeVideoMinFrameDuration == frameDuration {
            print("[CameraManager] FPS is already set to \(fps)")
            return
        }
        
        do {
            try device.lockForConfiguration()
            device.activeVideoMinFrameDuration = frameDuration
            device.activeVideoMaxFrameDuration = frameDuration
            device.unlockForConfiguration()
            print("[CameraManager] Set FPS to \(fps)")
        } catch {
            print("[CameraManager] Failed to lock device for FPS: \(error)")
        }
    }


    /// Capture a photo and return a UIImage via completion.
    /// - Parameters:
    ///   - completion: Called with the captured UIImage (full-res)
    func capturePhoto(completion: @escaping (UIImage?) -> Void) {
        print("[CameraManager] capturePhoto called; current preset = \(session.sessionPreset.rawValue)")
        
        let settings = AVCapturePhotoSettings()
        
        if #available(iOS 16.0, *) {
            settings.maxPhotoDimensions = CMVideoDimensions(width: 4032, height: 3024)
        } else {
            if photoOutput.isHighResolutionCaptureEnabled {
                settings.isHighResolutionPhotoEnabled = true
            }
        }
        
        photoOutput.maxPhotoQualityPrioritization = .quality
        
        self.photoCaptureCompletion = completion
        photoOutput.capturePhoto(with: settings, delegate: self)
    }

    func photoOutput(_ output: AVCapturePhotoOutput, didFinishProcessingPhoto photo: AVCapturePhoto, error: Error?) {
        guard error == nil, let data = photo.fileDataRepresentation(), let image = UIImage(data: data) else {
            photoCaptureCompletion?(nil)
            photoCaptureCompletion = nil
            return
        }
        photoCaptureCompletion?(image)
        photoCaptureCompletion = nil
        // Revert to preview format (15 FPS) after capture
        setFPS(15)
    }

    public func startSession() {
        configure()
        if !session.isRunning {
            DispatchQueue.global(qos: .userInitiated).async {
                self.session.startRunning()
            }
        }
    }
    public func stopSession() {
        if session.isRunning {
            DispatchQueue.global(qos: .userInitiated).async {
                self.session.stopRunning()
            }
        }
    }
}
