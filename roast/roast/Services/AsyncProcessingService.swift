import Foundation
import Combine

class AsyncProcessingService: ObservableObject {
    static let shared = AsyncProcessingService()
    
    @Published var hasDataReady = false
    @Published var pendingRequests: [AsyncRequest] = []
    @Published var readyRequestIds: Set<String> = []
    
    private let apiService = APIService.shared
    private let storageKey = "pending_async_requests"
    private let readyStorageKey = "ready_request_ids"
    
    // Reactive publishers for UI updates
    var hasPendingRequests: AnyPublisher<Bool, Never> {
        $pendingRequests
            .map { !$0.isEmpty }
            .eraseToAnyPublisher()
    }
    
    var processingCount: AnyPublisher<Int, Never> {
        $pendingRequests
            .map { requests in
                requests.filter { $0.status == .processing }.count
            }
            .eraseToAnyPublisher()
    }
    
    init() {
        loadPersistedState()
        setupReactiveUpdates()
    }
    
    // MARK: - State Management
    
    private func loadPersistedState() {
        // Load pending requests
        if let data = UserDefaults.standard.data(forKey: storageKey),
           let requests = try? JSONDecoder().decode([AsyncRequest].self, from: data) {
            pendingRequests = requests
        }
        
        // Load ready request IDs
        if let readyIds = UserDefaults.standard.array(forKey: readyStorageKey) as? [String] {
            readyRequestIds = Set(readyIds)
        }
        
        updateHasDataReady()
    }
    
    private func persistState() {
        // Save pending requests
        if let data = try? JSONEncoder().encode(pendingRequests) {
            UserDefaults.standard.set(data, forKey: storageKey)
        }
        
        // Save ready request IDs
        UserDefaults.standard.set(Array(readyRequestIds), forKey: readyStorageKey)
    }
    
    private func setupReactiveUpdates() {
        // Update hasDataReady when readyRequestIds changes
        $readyRequestIds
            .map { !$0.isEmpty }
            .assign(to: &$hasDataReady)
    }
    
    private func updateHasDataReady() {
        hasDataReady = !readyRequestIds.isEmpty
    }
    
    // MARK: - Request Submission
    
	func submitRequest(requestId: String, imageCount: Int) {
		let request = AsyncRequest(
			requestId: requestId,
			imageCount: imageCount
		)
        
		DispatchQueue.main.async {
			self.pendingRequests.append(request)
			self.persistState()
		}
		
		print("[AsyncProcessingService] Tracking request: \(requestId)")
    }
    
    // MARK: - Notification Processing
    
    func markRequestReady(_ requestId: String) {
        DispatchQueue.main.async {
            self.readyRequestIds.insert(requestId)
            
            // Update request status
            if let index = self.pendingRequests.firstIndex(where: { $0.requestId == requestId }) {
                self.pendingRequests[index].status = .ready
            }
            
            self.persistState()
            self.updateHasDataReady()
            
            // Update badge count
            PushNotificationService.shared.updateBadge(count: self.readyRequestIds.count)
            
            print("[AsyncProcessingService] Request \(requestId) marked as ready")
        }
    }
    
    // MARK: - Data Retrieval
    
    func fetchReadyResults() {
        guard !readyRequestIds.isEmpty else { return }
        
        let requestIdsToProcess = Array(readyRequestIds)
        
        for requestId in requestIdsToProcess {
            fetchResult(for: requestId)
        }
    }
    
    private func fetchResult(for requestId: String) {
        apiService.getRequest<AsyncResultResponse>(
            endpoint: "/api/ingredients/\(requestId)",
            timeoutInterval: 60
        ) { [weak self] (result: Result<AsyncResultResponse, APIError>) in
            switch result {
            case .success(let response):
                DispatchQueue.main.async {
                    self?.handleSuccessfulRetrieval(requestId: requestId, ingredients: response.ingredients)
                }
                
            case .failure(let error):
                DispatchQueue.main.async {
                    self?.handleRetrievalError(requestId: requestId, error: error)
                }
            }
        }
    }
    
    private func handleSuccessfulRetrieval(requestId: String, ingredients: [Ingredient]) {
        // Remove from ready and pending lists
        readyRequestIds.remove(requestId)
        pendingRequests.removeAll { $0.requestId == requestId }
        
        // Update app data
        DispatchQueue.main.async {
            // Assuming AppDataViewModel is accessible through a shared instance or dependency injection
            // For now, we'll use NotificationCenter to broadcast the update
            NotificationCenter.default.post(
                name: NSNotification.Name("AsyncIngredientsReceived"),
                object: nil,
                userInfo: ["ingredients": ingredients, "requestId": requestId]
            )
        }
        
        persistState()
        updateHasDataReady()
        
        // Update badge
        PushNotificationService.shared.updateBadge(count: readyRequestIds.count)
        
        // Show success notification
        ToastManager.shared.showSuccess(
            "Ingredients Ready!",
            message: "Found \(ingredients.count) ingredient\(ingredients.count == 1 ? "" : "s")"
        )
        
        print("[AsyncProcessingService] Successfully retrieved \(ingredients.count) ingredients for request \(requestId)")
    }
    
    private func handleRetrievalError(requestId: String, error: APIError) {
        switch error {
        case .serverError(let statusCode, _, _) where statusCode == 404:
            // Data expired - remove from tracking
            readyRequestIds.remove(requestId)
            pendingRequests.removeAll { $0.requestId == requestId }
            persistState()
            updateHasDataReady()
            
            ToastManager.shared.showError(
                "Data Expired",
                message: "Ingredient analysis expired. Please try again."
            )
            
        default:
            // Other errors - keep in ready state for retry
            ToastManager.shared.showError(
                "Retrieval Failed",
                message: "Failed to fetch results. Will retry automatically."
            )
            
            // Retry after delay
            DispatchQueue.main.asyncAfter(deadline: .now() + 30.0) {
                self.fetchResult(for: requestId)
            }
        }
        
        print("[AsyncProcessingService] Retrieval error for request \(requestId): \(error)")
    }
    
    // MARK: - Manual Operations
    
    func clearExpiredRequests() {
        let oneHourAgo = Date().addingTimeInterval(-3600)
        
        pendingRequests.removeAll { request in
            request.timestamp < oneHourAgo
        }
        
        persistState()
        updateHasDataReady()
    }
    
    func cancelRequest(_ requestId: String) {
        readyRequestIds.remove(requestId)
        pendingRequests.removeAll { $0.requestId == requestId }
        persistState()
        updateHasDataReady()
        
        // Update badge
        PushNotificationService.shared.updateBadge(count: readyRequestIds.count)
    }
    
    func clearAllRequests() {
        readyRequestIds.removeAll()
        pendingRequests.removeAll()
        persistState()
        updateHasDataReady()
        
        PushNotificationService.shared.clearBadge()
    }
    
    // MARK: - Testing & Simulation
    
    func simulateNotificationReceived(requestId: String) {
        print("[AsyncProcessingService] Simulating notification for request: \(requestId)")
        markRequestReady(requestId)
    }
}
