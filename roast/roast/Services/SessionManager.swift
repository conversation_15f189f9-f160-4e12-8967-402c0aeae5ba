// SessionManager.swift
import Combine
import Foundation

class SessionManager: ObservableObject {
    static let shared = SessionManager()
    private let keychainHelper = KeychainHelper.shared

    // Use your app's bundle identifier for the service name for uniqueness
    private let keychainService = Bundle.main.bundleIdentifier ?? "com.marshmellow.roast"
    private let sessionIdAccountKey = "app_session_id"  // This is the 'account' in Keychain terms

    let serverBaseURL = "https://dev.roast.marshmellow.store"

    @Published var currentSessionId: String?
    @Published var isLoadingSession: Bool = false  // To indicate session establishment is in progress

    private init() {
        currentSessionId = keychainHelper.loadString(
            service: keychainService, account: sessionIdAccountKey)
        print(
            "SessionManager initialized. Session ID from Keychain: \(currentSessionId != nil ? "\(currentSessionId!.prefix(8))..." : "Not set")"
        )
    }

    func ensureSession(completion: @escaping (String?) -> Void) {
        DispatchQueue.main.async {
            self.isLoadingSession = true
        }

        if let existingId = currentSessionId, !existingId.isEmpty {
            print(
                "SessionManager: Using existing session ID from memory: \(existingId.prefix(8))...")
            DispatchQueue.main.async {
                self.isLoadingSession = false
            }
            completion(existingId)
            return
        }

        if let keychainId = keychainHelper.loadString(
            service: keychainService, account: sessionIdAccountKey), !keychainId.isEmpty
        {
            print(
                "SessionManager: Using existing session ID from Keychain: \(keychainId.prefix(8))..."
            )
            DispatchQueue.main.async {
                self.currentSessionId = keychainId
                self.isLoadingSession = false
            }
            completion(keychainId)
            return
        }

        print("SessionManager: No existing session ID found, creating a new one...")
        createSessionOnServer { [weak self] newSessionId in
            guard let self = self else {
                DispatchQueue.main.async { self?.isLoadingSession = false }
                completion(nil)
                return
            }

            DispatchQueue.main.async {
                if let newId = newSessionId {
                    if self.keychainHelper.saveString(
                        newId, service: self.keychainService, account: self.sessionIdAccountKey)
                    {
                        self.currentSessionId = newId
                        print(
                            "SessionManager: New session ID created and saved to Keychain: \(newId.prefix(8))..."
                        )
                    } else {
                        // Fallback or error handling if keychain save fails
                        print(
                            "SessionManager: Failed to save new session ID to Keychain. Using in-memory only for now."
                        )
                        self.currentSessionId = newId  // Still use it in memory
                    }
                } else {
                    print("SessionManager: Failed to create new session ID from server.")
                }
                self.isLoadingSession = false
                completion(newSessionId)
            }
        }
    }

    private func createSessionOnServer(completion: @escaping (String?) -> Void) {
        guard let url = URL(string: "\(serverBaseURL)/session") else {
            print("SessionManager Error: Invalid URL for /session endpoint")
            completion(nil)
            return
        }

        var request = URLRequest(url: url)
        request.httpMethod = "GET"

        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                print("SessionManager Error fetching session_id: \(error.localizedDescription)")
                completion(nil)
                return
            }

            guard let httpResponse = response as? HTTPURLResponse,
                (200...299).contains(httpResponse.statusCode)
            else {
                let statusCode = (response as? HTTPURLResponse)?.statusCode ?? -1
                print("SessionManager Error fetching session_id: HTTP status code \(statusCode)")
                if let data = data, let errorBody = String(data: data, encoding: .utf8) {
                    print("SessionManager Error body: \(errorBody)")
                }
                completion(nil)
                return
            }

            guard let data = data else {
                print("SessionManager Error fetching session_id: No data received")
                completion(nil)
                return
            }

            do {
                if let json = try JSONSerialization.jsonObject(with: data, options: [])
                    as? [String: Any],
                    let sessionId = json["session_id"] as? String
                {
                    completion(sessionId)
                } else {
                    print(
                        "SessionManager Error fetching session_id: Could not parse JSON or find session_id key"
                    )
                    if let responseStr = String(data: data, encoding: .utf8) {
                        print("SessionManager Response: \(responseStr)")
                    }
                    completion(nil)
                }
            } catch {
                print(
                    "SessionManager Error fetching session_id: JSON decoding failed: \(error.localizedDescription)"
                )
                completion(nil)
            }
        }.resume()
    }

    func clearSession() {
        keychainHelper.delete(service: keychainService, account: sessionIdAccountKey)
        DispatchQueue.main.async {
            self.currentSessionId = nil
        }
        print("SessionManager: Session ID cleared from Keychain and memory.")
    }

}
