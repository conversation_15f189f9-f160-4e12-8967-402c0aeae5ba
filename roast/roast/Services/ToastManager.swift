import Foundation
import Combine

class ToastManager: ObservableObject {
    static let shared = ToastManager()
    
    @Published var currentToast: ToastMessage?
    
    private var cancellables = Set<AnyCancellable>()
    
    private init() {}
    
    func showToast(_ toast: ToastMessage) {
        DispatchQueue.main.async {
            self.currentToast = toast
            
            // Auto-dismiss after duration
            Timer.publish(every: toast.duration, on: .main, in: .common)
                .autoconnect()
                .first()
                .sink { _ in
                    self.dismissToast()
                }
                .store(in: &self.cancellables)
        }
    }
    
    func showError(_ title: String, message: String? = nil, duration: TimeInterval = 4.0) {
        let toast = ToastMessage(type: .error, title: title, message: message, duration: duration)
        showToast(toast)
    }
    
    func showSuccess(_ title: String, message: String? = nil, duration: TimeInterval = 3.0) {
        let toast = ToastMessage(type: .success, title: title, message: message, duration: duration)
        showToast(toast)
    }
    
    func showWarning(_ title: String, message: String? = nil, duration: TimeInterval = 3.5) {
        let toast = ToastMessage(type: .warning, title: title, message: message, duration: duration)
        showToast(toast)
    }
    
    func showInfo(_ title: String, message: String? = nil, duration: TimeInterval = 3.0) {
        let toast = ToastMessage(type: .info, title: title, message: message, duration: duration)
        showToast(toast)
    }
    
    func dismissToast() {
        DispatchQueue.main.async {
            self.currentToast = nil
        }
    }
}
