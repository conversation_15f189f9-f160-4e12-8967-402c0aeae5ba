import Foundation

protocol StorageManagerProtocol {
    // Single item operations
    func save<T: Encodable>(_ item: T, forKey key: String) throws
    func loadItem<T: Decodable>(forKey key: String) throws -> T
    
    // Array operations
    func save<T: Encodable>(_ items: [T], forKey key: String) throws
    func loadArray<T: Decodable>(forKey key: String) throws -> [T]
    
    // File operations
    func remove(forKey key: String)
    func exists(forKey key: String) -> Bool
}

final class StorageManager: StorageManagerProtocol {
    static let shared = StorageManager()
    
    private let fileManager: FileManager
    private let decoder: JSONDecoder
    private let encoder: JSONEncoder
    
    private init(fileManager: FileManager = .default) {
        self.fileManager = fileManager
        self.decoder = JSONDecoder()
        self.encoder = JSONEncoder()
    }
    
    private func getDocumentsURL() -> URL {
        fileManager
            .urls(for: .documentDirectory, in: .userDomainMask)
            .first!
    }
    
    private func fileURL(for key: String) -> URL {
        getDocumentsURL().appendingPathComponent("\(key).json")
    }
    
    // Ensure method signatures exactly match the protocol
    func save<T: Encodable>(_ item: T, forKey key: String) throws {
        do {
            let data = try encoder.encode(item)
            try data.write(to: fileURL(for: key))
        } catch {
            throw AppError.storage(.saveFailure(error.localizedDescription))
        }
    }
    
    func save<T: Encodable>(_ items: [T], forKey key: String) throws {
        do {
            let data = try encoder.encode(items)
            try data.write(to: fileURL(for: key))
        } catch {
            throw AppError.storage(.saveFailure(error.localizedDescription))
        }
    }
    
    func loadItem<T: Decodable>(forKey key: String) throws -> T {
        let url = fileURL(for: key)
        
        guard fileManager.fileExists(atPath: url.path) else {
            throw AppError.storage(.notFound)
        }
        
        do {
            let data = try Data(contentsOf: url)
            return try decoder.decode(T.self, from: data)
        } catch {
            throw AppError.storage(.loadFailure(error.localizedDescription))
        }
    }
    
    func loadArray<T: Decodable>(forKey key: String) throws -> [T] {
        let url = fileURL(for: key)
        
        guard fileManager.fileExists(atPath: url.path) else {
            return []
        }
        
        do {
            let data = try Data(contentsOf: url)
            return try decoder.decode([T].self, from: data)
        } catch {
            throw AppError.storage(.loadFailure(error.localizedDescription))
        }
    }
    
    func remove(forKey key: String) {
        try? fileManager.removeItem(at: fileURL(for: key))
    }
    
    func exists(forKey key: String) -> Bool {
        fileManager.fileExists(atPath: fileURL(for: key).path)
    }
}

// Storage Keys
extension StorageManager {
    enum Keys {
        static let inventory = "inventory"
        static let recipes = "recipes"
        static let settings = "settings"
        static let session = "session"
    }
}
