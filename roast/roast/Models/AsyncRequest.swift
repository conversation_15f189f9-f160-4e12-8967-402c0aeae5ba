import Foundation

// MARK: - Async Processing Models

struct AsyncRequest: Identifiable, Codable {
    let id: UUID
    let requestId: String // Server-provided UUID for tracking
    let timestamp: Date
    let imageCount: Int
    var status: AsyncRequestStatus
    
    init(requestId: String, imageCount: Int) {
        self.id = UUID()
        self.requestId = requestId
        self.timestamp = Date()
        self.imageCount = imageCount
        self.status = .processing
    }
}

enum AsyncRequestStatus: String, Codable, CaseIterable {
    case processing
    case ready
    case completed
    case expired
    case failed
}

// MARK: - API Response Models

struct AsyncSubmissionResponse: Codable {
    let request_id: String
}

struct AsyncResultResponse: Codable {
    let ingredients: [Ingredient]
    let request_id: String
}

// MARK: - Push Token Registration

struct PushTokenRequest: Codable {
    let push_token: String
}
