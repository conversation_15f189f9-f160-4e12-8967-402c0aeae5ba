import Foundation

enum AppError: LocalizedError {
    case storage(StorageError)
    case camera(CameraError)
    case network(NetworkError)
    case image(ImageError)
    case parsing(ParsingError)
    case unknown(String)
    
    enum StorageError {
        case saveFailure(String)
        case loadFailure(String)
        case invalidData
        case notFound
    }
    
    enum CameraError {
        case notAuthorized
        case captureFailure
        case unavailable
    }
    
    enum NetworkError {
        case invalidResponse
        case connectionFailure
        case timeout
        case serverError(Int)
    }
    
    enum ImageError {
        case processingFailed
        case invalidFormat
        case compressionFailed
    }

    enum ParsingError {
        case emptyResponse
        case invalidData
    }
    
    var errorDescription: String? {
        switch self {
        case .storage(let error):
            switch error {
            case .saveFailure(let detail): return "Failed to save data: \(detail)"
            case .loadFailure(let detail): return "Failed to load data: \(detail)"
            case .invalidData: return "Invalid data format"
            case .notFound: return "Data not found"
            }
        case .camera(let error):
            switch error {
            case .notAuthorized: return "Camera access not authorized"
            case .captureFailure: return "Failed to capture photo"
            case .unavailable: return "Camera is unavailable"
            }
        case .network(let error):
            switch error {
            case .invalidResponse: return "Invalid server response"
            case .connectionFailure: return "Connection failed"
            case .timeout: return "Request timed out - server is taking too long to process"
            case .serverError(let code): return "Server error: \(code)"
            }
        case .image(let error):
            switch error {
            case .processingFailed: return "Image processing failed"
            case .invalidFormat: return "Invalid image format"
            case .compressionFailed: return "Image compression failed"
            }
        case .parsing(let error): // Add this new case
            switch error {
            case .emptyResponse: return "Server response contained no ingredients."
            case .invalidData: return "Failed to parse server response data."
            }
        case .unknown(let message):
            return message
        }
    }
}
