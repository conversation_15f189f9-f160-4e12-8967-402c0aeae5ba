import SwiftUI

// MARK: - Ingredient Model
struct Ingredient: Identifiable, Codable, Equatable, Hashable {
    let id: UUID
    var item_location: String
    var brand: String?
    var name: String
    var food_category: String
    var dietary_attributes: String?
    var quantity_value: Float
    var unit: String
    var container: String
    var expiration_date: String?
    var is_confident: Bool
    var note: String?

    init(
        id: UUID = UUID(),
        item_location: String,
        brand: String?,
        name: String,
        food_category: String,
        dietary_attributes: String?,
        quantity_value: Float,
        unit: String,
        container: String,
        expiration_date: String?,
        is_confident: Bool,
        note: String?
    ) {
        self.id = id
        self.item_location = item_location
        self.brand = brand
        self.name = name
        self.food_category = food_category
        self.dietary_attributes = dietary_attributes
        self.quantity_value = quantity_value
        self.unit = unit
        self.container = container
        self.expiration_date = expiration_date
        self.is_confident = is_confident
        self.note = note
    }

    // Custom decoder to handle missing id for migration
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        self.id = try container.decodeIfPresent(UUID.self, forKey: .id) ?? UUID()
        self.item_location = try container.decode(String.self, forKey: .item_location)
        self.brand = try container.decodeIfPresent(String.self, forKey: .brand)
        self.name = try container.decode(String.self, forKey: .name)
        self.food_category = try container.decode(String.self, forKey: .food_category)
        self.dietary_attributes = try container.decodeIfPresent(String.self, forKey: .dietary_attributes)
        self.quantity_value = try container.decode(Float.self, forKey: .quantity_value)
        self.unit = try container.decode(String.self, forKey: .unit)
        self.container = try container.decode(String.self, forKey: .container)
        self.expiration_date = try container.decodeIfPresent(String.self, forKey: .expiration_date)
        self.is_confident = try container.decode(Bool.self, forKey: .is_confident)
        self.note = try container.decodeIfPresent(String.self, forKey: .note)
    }
    
    enum CodingKeys: String, CodingKey {
        case id, item_location, brand, name, food_category, dietary_attributes, quantity_value, unit, container, expiration_date, is_confident, note
    }
}
