import SwiftUI

struct RecipesRequest: Encodable {
    let ingredients: String
    let message: String?
}

struct RecipesResponse: Codable {
    let recipes: [RecipeSeed]
}

struct RecipeSeed: Identifiable, Codable, Equatable, Hashable {
    let id: UUID
    var name: String
    var description: String
    var origin: String
    var duration: String
    var difficulty: String
    var instructions: [String]
    var flavor_profile: String
    var key_tags: [String]
    var ingredients: [String]
    static let mock = RecipeSeed(
        id: UUID(uuidString: "00000000-0000-0000-0000-000000000000")!,
        name: "Chocolate Cake",
        description: "A rich and moist chocolate cake.",
        origin: "American",
        duration: "45 min",
        difficulty: "Medium",
        instructions: [
            "Preheat oven to 350°F (175°C)",
            "Mix dry ingredients",
            "Add wet ingredients",
            "Pour into pan and bake for 30 minutes",
        ],
        flavor_profile: "Sweet",
        key_tags: ["Dessert", "Chocolate", "Baking"],
        ingredients: ["Flour", "Cocoa Powder", "Sugar", "Eggs", "Butter"]
    )
}

struct RecipeRequest: Encodable {
    let recipe_seed: RecipeSeed
    let available_ingredients: [String]
    let message: String?
}

struct RecipeResponse: Codable {
    let recipe: Recipe
}

struct Recipe: Identifiable, Codable, Equatable, Hashable {
    let id: UUID
    var recipeSeedId: UUID
    var name: String
    var prep_time: String?
    var cook_time: String
    var servings: Int
    var ingredients: [String]
    var instructions: [String]
}

struct StoredRecipe: Identifiable, Codable {
    var id: UUID
    var recipeSeed: RecipeSeed
    var recipe: Recipe
}

#if DEBUG
    extension Recipe {
        static var mock: Recipe {
            Recipe(
                id: UUID(),
                recipeSeedId: RecipeSeed.mock.id,
                name: "Mock Recipe",
                prep_time: "10 min",
                cook_time: "20 min",
                servings: 2,
                ingredients: [
                    "2 cups mock ingredient A",
                    "1 tbsp mock ingredient B",
                    "Pinch of mock spice",
                ],
                instructions: [
                    "1. Mix all mock ingredients together",
                    "2. Bake until golden.",
                    "3. Serve and enjoy!",
                ]
            )
        }
    }
#endif
