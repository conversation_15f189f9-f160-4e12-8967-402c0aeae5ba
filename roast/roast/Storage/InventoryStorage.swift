import Foundation

struct InventoryStorage {
    static let shared = StorageManager.shared

    static func loadInventory() -> [Ingredient] {
        do {
            return try shared.loadArray(forKey: StorageManager.Keys.inventory)
        } catch {
            print("[InventoryStorage] Load error: \(error.localizedDescription)")
            return []
        }
    }

    static func saveInventory(_ inventory: [Ingredient]) {
        do {
            try shared.save(inventory, forKey: StorageManager.Keys.inventory)
        } catch {
            print("[InventoryStorage] Save error: \(error.localizedDescription)")
        }
    }
}
