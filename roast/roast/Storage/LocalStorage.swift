import Foundation

struct LocalStorage {
	private static func fileURL(for filename: String) -> URL {
		let documents = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)
			.first!
		return documents.appendingPathComponent(filename)
	}

	static func load<T: Codable>(from filename: String) -> [T] {
		do {
			let data = try Data(contentsOf: fileURL(for: filename))
			let items = try JSONDecoder().decode([T].self, from: data)
			print("[LocalStorage] Loaded \(filename) successfully.")
			return items
		} catch {
			print("[LocalStorage] Failed to load \(filename): \(error)")
			return []
		}
		// } catch let error as NSError {
		//     if error.code != NSFileReadNoSuchFileError { // 260
		//         print("[LocalStorage] Failed to load \(filename): \(error)")
		//     }
		//     return []
		// }
	}

	static func save<T: Codable>(_ items: [T], to filename: String) {
		do {
			let data = try JSONEncoder().encode(items)
			try data.write(to: fileURL(for: filename), options: [.atomicWrite])
			print("[LocalStorage] File \(filename) saved successfully.")
		} catch {
			print("[LocalStorage] Failed to save \(filename): \(error)")
		}
	}
}
