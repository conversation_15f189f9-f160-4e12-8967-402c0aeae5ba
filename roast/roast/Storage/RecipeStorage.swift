import Foundation

struct RecipeStorage {
	static let shared = StorageManager.shared

	static func loadRecipes() -> [StoredRecipe] {
		do {
            return try shared.loadArray(forKey: StorageManager.Keys.recipes)
		} catch {
			print("[RecipeStorage] Load error: \(error.localizedDescription)")
			return []
		}
	}

	static func saveRecipes(_ recipes: [StoredRecipe]) {
		do {
            try shared.save(recipes, forKey: StorageManager.Keys.recipes)
		} catch {
			print("[RecipeStorage] Save error: \(error.localizedDescription)")
		}
	}
}
