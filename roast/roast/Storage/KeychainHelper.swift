// KeychainHelper.swift
import Foundation
import Security

class KeychainHelper {
    static let shared = KeychainHelper()
    private init() {}

    private func baseQuery(service: String, account: String) -> [String: Any] {
        var query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: account,
        ]
        // Add kSecAttrAccessible for background access if needed,
        // but for session ID, default is usually fine.
        // query[kSecAttrAccessible as String] =
        // kSecAttrAccessibleWhenUnlockedThisDeviceOnly
        return query
    }

    func save(data: Data, service: String, account: String) -> Bool {
        // Delete existing item first to handle updates correctly
        delete(service: service, account: account)

        var query = baseQuery(service: service, account: account)
        query[kSecValueData as String] = data
        // kSecAttrAccessible specifies when the keychain item should be readable.
        // kSecAttrAccessibleWhenUnlockedThisDeviceOnly is a good default for many cases.
        query[kSecAttrAccessible as String] = kSecAttrAccessibleWhenUnlockedThisDeviceOnly

        let status = SecItemAdd(query as CFDictionary, nil)
        if status == errSecSuccess {
            print(
                "KeychainHelper: Successfully saved data for account '\(account)' in service '\(service)'."
            )
            return true
        } else {
            print(
                "KeychainHelper: Error saving data for account '\(account)' in service '\(service)'. Status: \(status). OSStatus message: \(SecCopyErrorMessageString(status, nil) ?? "Unknown error" as CFString)"
            )
            return false
        }
    }

    func load(service: String, account: String) -> Data? {
        var query = baseQuery(service: service, account: account)
        query[kSecMatchLimit as String] = kSecMatchLimitOne
        query[kSecReturnData as String] = kCFBooleanTrue

        var item: CFTypeRef?
        let status = SecItemCopyMatching(query as CFDictionary, &item)

        if status == errSecSuccess {
            print(
                "KeychainHelper: Successfully loaded data for account '\(account)' from service '\(service)'."
            )
            return item as? Data
        } else if status == errSecItemNotFound {
            print("KeychainHelper: No data found for account '\(account)' in service '\(service)'.")
            return nil
        } else {
            print(
                "KeychainHelper: Error loading data for account '\(account)' from service '\(service)'. Status: \(status). OSStatus message: \(SecCopyErrorMessageString(status, nil) ?? "Unknown error" as CFString)"
            )
            return nil
        }
    }

    func delete(service: String, account: String) -> Bool {
        let query = baseQuery(service: service, account: account)
        let status = SecItemDelete(query as CFDictionary)

        if status == errSecSuccess || status == errSecItemNotFound {
            if status == errSecSuccess {
                print(
                    "KeychainHelper: Successfully deleted data for account '\(account)' in service '\(service)'."
                )
            }
            return true  // Item deleted or was not found, which is fine for a delete operation.
        } else {
            print(
                "KeychainHelper: Error deleting data for account '\(account)' in service '\(service)'. Status: \(status). OSStatus message: \(SecCopyErrorMessageString(status, nil) ?? "Unknown error" as CFString)"
            )
            return false
        }
    }

    // Convenience methods for String
    func saveString(_ string: String, service: String, account: String) -> Bool {
        guard let data = string.data(using: .utf8) else {
            print("KeychainHelper: Could not convert string to data for account '\(account)'.")
            return false
        }
        return save(data: data, service: service, account: account)
    }

    func loadString(service: String, account: String) -> String? {
        guard let data = load(service: service, account: account) else {
            return nil
        }
        return String(data: data, encoding: .utf8)
    }
}
