import SwiftUI

class AppCoordinator: ObservableObject {
    @Published var appDataVM: AppDataViewModel
    @Published var cameraVM: CameraViewModel
    @Published var ingredientsVM: IngredientsViewModel
    @Published var recipesVM: RecipesViewModel

    init() {
        let appDataVM = AppDataViewModel()
        self.appDataVM = appDataVM
        self.cameraVM = CameraViewModel(appDataVM: appDataVM)
        self.ingredientsVM = IngredientsViewModel(appDataVM: appDataVM)
        self.recipesVM = RecipesViewModel(appDataVM: appDataVM)
    }
}
