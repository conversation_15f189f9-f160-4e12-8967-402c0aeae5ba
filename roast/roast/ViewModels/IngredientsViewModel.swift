import SwiftUI

// Ingredients VM
class IngredientsViewModel: ObservableObject {
    @ObservedObject var appDataVM: AppDataViewModel
    let recipesEndpoint: String = "/api/recipes"
    @Published var isSending: Bool = false
    @Published var sendError: String? = nil

    // @Binding var inventory: [Ingredient]
    @Published var selectedIngredients: Set<Ingredient> = []

    init(appDataVM: AppDataViewModel) {
        self.appDataVM = appDataVM
    }

    // MARK: - Toggle Ingredient
    func toggleSelection(for ingredient: Ingredient) {
        if let idx = selectedIngredients.firstIndex(where: { $0.id == ingredient.id }) {
            selectedIngredients.remove(at: idx)
        } else {
            selectedIngredients.insert(ingredient)
        }
    }

    // MARK: - Update Ingredient
    func updateIngredient(_ updated: Ingredient) {
        print("[IngredientsViewModel] Updating ingredient: \(updated.name) - \(updated.id)")
        if let idx = appDataVM.inventory.firstIndex(where: { $0.id == updated.id }) {
            print("[IngredientsViewModel] Found ingredient at index: \(idx)")
            appDataVM.inventory[idx] = updated
        } else {
            print(
                "[IngredientsViewModel] No match found for ingredient: \(updated.name) - \(updated.id)"
            )
        }
        // if let selIdx = selectedIngredients.firstIndex(where: { $0.id == updated.id }) {
        //     selectedIngredients.remove(at: selIdx)
        //     selectedIngredients.insert(updated)
        // }
    }

    // MARK: - Delete Ingredient
    func deleteIngredient(_ ingredient: Ingredient) {
        print("[IngredientsViewModel] Deleting ingredient: \(ingredient.name) - \(ingredient.id)")
        if let idx = appDataVM.inventory.firstIndex(where: { $0.id == ingredient.id }) {
            appDataVM.inventory.remove(at: idx)
        }
        if let selIdx = selectedIngredients.firstIndex(where: { $0.id == ingredient.id }) {
            selectedIngredients.remove(at: selIdx)
        }
    }

    // MARK: - Test Recipe View
    func testRecipeView(completion: @escaping (String) -> Void) {
        guard !selectedIngredients.isEmpty else {
            completion("No ingredients selected")
            return
        }
        // Format selectedIngredients into string list
        let formattedIngredientsList = selectedIngredients.map { ingredient -> String in
            let dietary =
                ingredient.dietary_attributes?.isEmpty == false
                ? ingredient.dietary_attributes! : ""
            return
                "- \(ingredient.name) \(dietary) \(ingredient.quantity_value.clean) \(ingredient.unit)"
        }.joined(separator: "\n")
        print("[IngredientsViewModel] Ingredients List:\n\(formattedIngredientsList)")

        do {
            // encode formattedIngredientsList to JSON
            let encoder = JSONEncoder()
            let data = try encoder.encode(formattedIngredientsList)
            let jsonData = String(data: data, encoding: .utf8)
            completion(jsonData ?? "Failed to encode ingredients.")
        } catch {
            DispatchQueue.main.async {
                self.sendError = "Failed to encode ingredients."
                completion("Failed to encode ingredients.")
            }
            return
        }
    }

    // MARK: - Send Ingredients
    // Sends selected ingredients to the server endpoint
    func sendSelectedIngredients(completion: @escaping (Bool) -> Void) {
        guard !selectedIngredients.isEmpty else {
            completion(false)
            return
        }

        isSending = true
        sendError = nil

        // Format selectedIngredients into string list
        let formattedIngredientsList = selectedIngredients.map { ingredient -> String in
            let dietary =
                ingredient.dietary_attributes?.isEmpty == false
                ? ingredient.dietary_attributes! : ""
            return
                "- \(ingredient.name) \(dietary) \(ingredient.quantity_value.clean) \(ingredient.unit)"
        }.joined(separator: "\n")

        let payload = RecipesRequest(
            ingredients: formattedIngredientsList,
            message: ""
        )

        APIService.shared.postRequest(
            endpoint: recipesEndpoint,
            body: payload,
            timeoutInterval: 300,  // 5 minutes
        ) { [weak self] (result: Result<RecipesResponse, APIError>) in
            DispatchQueue.main.async {
                self?.isSending = false
            }
            switch result {
            case .success(let response):
                DispatchQueue.main.async {
                    self?.appDataVM.recipeSeeds = response.recipes
                    completion(true)
                }
            case .failure(let error):
                DispatchQueue.main.async {
                    self?.sendError = error.errorDescription
                    completion(false)
                }
            }
        }
    }
}
