import SwiftUI

class RecipesViewModel: ObservableObject {
    @ObservedObject var appDataVM: AppDataViewModel
    let recipeEndpoint: String = "/api/recipe"
    @Published var isSending: Bool = false
    @Published var sendError: String? = nil

    init(appDataVM: AppDataViewModel) {
        self.appDataVM = appDataVM
    }

    // MARK: - Create Recipe Request
    func createRecipeRequest(
        for recipe: RecipeSeed, availableIngredients: [String], message: String? = nil,
        completion: @escaping (Recipe?) -> Void
    ) {
        isSending = true
        sendError = nil

        let payload = RecipeRequest(
            recipe_seed: recipe,
            available_ingredients: availableIngredients,
            message: message
        )

        APIService.shared.postRequest(
            endpoint: recipeEndpoint,
            body: payload,
            timeoutInterval: 300  // 5 minutes
        ) { [weak self] (result: Result<RecipeResponse, APIError>) in
            DispatchQueue.main.async {
                self?.isSending = false
            }

            switch result {
            case .success(let response):
                DispatchQueue.main.async { [weak self] in
                    // self?.detailedRecipe = response.recipe
                    // self?.suggestedRecipes[recipe.id] = response.recipe
                    guard let self = self else { return }
                    let storedRecipe = StoredRecipe(
                        id: recipe.id, recipeSeed: recipe, recipe: response.recipe)
                    self.appDataVM.storedRecipes.append(storedRecipe)
                    completion(response.recipe)
                }
            case .failure(let error):
                DispatchQueue.main.async {
                    self?.sendError = error.errorDescription
                    completion(nil)
                }
            }
        }
    }
}
