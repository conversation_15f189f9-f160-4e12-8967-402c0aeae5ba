import SwiftUI

class AppDataViewModel: ObservableObject {
    // @Published var step: AppFlowStep = .camera
    @Published var inventory: [Ingredient] = InventoryStorage.loadInventory() {
        didSet {
            InventoryStorage.saveInventory(inventory)
        }
    }
    @Published var recipeSeeds: [RecipeSeed] = []

    // @Published var storedRecipes: [StoredRecipe] = [] {
    @Published var storedRecipes: [StoredRecipe] = RecipeStorage.loadRecipes() {
        didSet {
            RecipeStorage.saveRecipes(storedRecipes)
        }
    }
}
