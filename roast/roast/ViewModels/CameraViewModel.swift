import Foundation
import SwiftUI

struct ImagesRequest: Encodable {
    let images: [String]
    let message: String?
}

class CameraViewModel: ObservableObject {
    // Dependencies
    private weak var appDataVM: AppDataViewModel?
    private let imageProcessor = ImageProcessingService.shared
    private let inventoryParser = InventoryParsingService.shared
    private let apiService = APIService.shared
    let cameraManager = CameraManager()

    // Constants
    private let inventoryEndpoint = "/api/ingredients"
    private let thumbnailSize: CGFloat

    // Published state
    @Published var isCaptureButtonPressed = false
    @Published var overlayOpacity = 1.0
    @Published var isSpinnerLoading = false
    @Published var thumbnails: [Thumbnail] = []
    @Published var highResImages: [UIImage] = []
    @Published var isWaitingForServerResponse = false
    @Published private(set) var error: AppError?
    @Published var showShareSheet = false
    @Published var exportURL: URL?

    // Computed properties
    var thumbnailHeight: CGFloat { thumbnailSize }
    var thumbnailWidth: CGFloat { thumbnailSize * (9.0 / 16.0) }

    // Callbacks
    var onSendSuccess: (([Ingredient]) -> Void)?

    init(appDataVM: AppDataViewModel, thumbnailSize: CGFloat = 112) {
        self.appDataVM = appDataVM
        self.thumbnailSize = thumbnailSize
        loadPersistedImages()
    }

    // MARK: - Persistence

    private var documentsDirectory: URL {
        FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
    }

    private var imagesDirectory: URL {
        documentsDirectory.appendingPathComponent("CapturedImages")
    }

    private func ensureImagesDirectoryExists() {
        try? FileManager.default.createDirectory(
            at: imagesDirectory, withIntermediateDirectories: true)
    }

    private func saveImageToDisk(_ image: UIImage, filename: String) {
        ensureImagesDirectoryExists()
        let url = imagesDirectory.appendingPathComponent(filename)
        if let data = image.jpegData(compressionQuality: 0.8) {
            try? data.write(to: url)
        }
    }

    private func loadImageFromDisk(filename: String) -> UIImage? {
        let url = imagesDirectory.appendingPathComponent(filename)
        guard let data = try? Data(contentsOf: url) else { return nil }
        return UIImage(data: data)
    }

    private func deleteImageFromDisk(filename: String) {
        let url = imagesDirectory.appendingPathComponent(filename)
        try? FileManager.default.removeItem(at: url)
    }

    private func loadPersistedImages() {
        ensureImagesDirectoryExists()

        guard
            let files = try? FileManager.default.contentsOfDirectory(
                at: imagesDirectory,
                includingPropertiesForKeys: [.creationDateKey],
                options: .skipsHiddenFiles)
        else { return }

        // Sort by creation date (newest first)
        let sortedFiles = files.sorted { file1, file2 in
            let date1 =
                (try? file1.resourceValues(forKeys: [.creationDateKey]))?.creationDate
                ?? Date.distantPast
            let date2 =
                (try? file2.resourceValues(forKeys: [.creationDateKey]))?.creationDate
                ?? Date.distantPast
            return date1 > date2
        }

        for fileURL in sortedFiles {
            let filename = fileURL.lastPathComponent

            if let highResImage = loadImageFromDisk(filename: filename),
                let thumbnail = imageProcessor.generateThumbnail(
                    from: highResImage, maxDimension: thumbnailSize)
            {

                thumbnails.append(Thumbnail(image: thumbnail, isLoading: false))
                highResImages.append(highResImage)
            }
        }
    }

    func handleCapture() {
        thumbnails.insert(Thumbnail(image: nil, isLoading: true), at: 0)

        cameraManager.capturePhoto { [weak self] image in
            guard let self = self, let image = image else { return }

            DispatchQueue.global(qos: .userInitiated).async {
                if let thumbnail = self.imageProcessor.generateThumbnail(
                    from: image, maxDimension: self.thumbnailSize)
                {
                    // Save image to disk
                    let filename = "image_\(Date().timeIntervalSince1970).jpg"
                    self.saveImageToDisk(image, filename: filename)

                    DispatchQueue.main.async {
                        if !self.thumbnails.isEmpty, self.thumbnails[0].isLoading {
                            self.thumbnails[0] = Thumbnail(image: thumbnail, isLoading: false)
                        }
                        self.highResImages.insert(image, at: 0)
                    }
                }
            }
        }
    }

    // MARK: - Async Processing
    
    func sendImagesAsync() {
        let imagesBase64: [String]

        imagesBase64 = imageProcessor.convertToBase64(images: highResImages)

        let payload = ImagesRequest(images: imagesBase64, message: "")

        isWaitingForServerResponse = false // Switch to async mode
        error = nil

        apiService.postRequest<AsyncSubmissionResponse, ImagesRequest>(
            endpoint: "/api/ingredients",
            body: payload,
            timeoutInterval: 60 // Shorter timeout for submission
        ) { [weak self] (result: Result<AsyncSubmissionResponse, APIError>) in
            guard let self = self else { return }

            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    // Store request for tracking
                    AsyncProcessingService.shared.submitRequest(
                        requestId: response.request_id,
                        imageCount: self.highResImages.count
                    )

                    self.clearImages()

                    // Show immediate confirmation
                    ToastManager.shared.showSuccess(
                        "Images Submitted",
                        message: "You'll be notified when processing is complete!"
                    )

                    print("[CameraViewModel] Async submission successful: \(response.request_id)")

                case .failure(let apiError):
                    self.handleAPIError(apiError)
                }
            }
        }
    }

    func sendImages() {
        let imagesBase64: [String]

        imagesBase64 = imageProcessor.convertToBase64(images: highResImages)

        let payload = ImagesRequest(images: imagesBase64, message: "")

        // Calculate dynamic timeout based on number of images
        // Base timeout of 90 seconds + 45 seconds per additional image after the first
        let baseTimeout: TimeInterval = 180
        let perImageTimeout: TimeInterval = 60
        let dynamicTimeout =
            baseTimeout + (perImageTimeout * max(0, TimeInterval(highResImages.count - 1)))

        print(
            "[CameraViewModel] Sending \(highResImages.count) images with timeout of \(dynamicTimeout) seconds"
        )

        isWaitingForServerResponse = true
        error = nil

        apiService.postRequest(
            endpoint: inventoryEndpoint,
            body: payload,
            timeoutInterval: dynamicTimeout
        ) { [weak self] (result: Result<InventoryParsingService.ServerResponse, APIError>) in
            guard let self = self else { return }

            DispatchQueue.main.async {
                self.isWaitingForServerResponse = false

                switch result {
                case .success(let response):
                    if let inventory = self.processInventoryResponse(response) {
                        self.appDataVM?.inventory = inventory
                        self.onSendSuccess?(inventory)
                        self.clearImages()

                        // Show success toast
                        ToastManager.shared.showSuccess(
                            "Images Processed",
                            message:
                                "Found \(inventory.count) ingredient\(inventory.count == 1 ? "" : "s")"
                        )
                    }
                case .failure(let apiError):
                    self.handleAPIError(apiError)
                }
            }
        }
    }

    private func handleAPIError(_ apiError: APIError) {
        switch apiError {
        case .gatewayTimeout:
            // Don't show additional error since APIService already showed a toast
            self.error = AppError.network(.timeout)
        case .serverError(let statusCode, _, let message):
            // Show toast for server errors that aren't handled by APIService
            if statusCode != 429 && statusCode != 504 {  // These are handled by APIService
                ToastManager.shared.showError(
                    "Server Error",
                    message: message ?? "Server returned error \(statusCode)",
                    duration: 5.0
                )
            }
            self.error = AppError.network(.serverError(statusCode))
        case .requestFailed(let underlyingError):
            // Check if it's a timeout
            if (underlyingError as NSError).code == NSURLErrorTimedOut {
                // APIService already showed toast for timeout
                self.error = AppError.network(.timeout)
            } else {
                ToastManager.shared.showError(
                    "Network Error",
                    message: "Request failed: \(underlyingError.localizedDescription)"
                )
                self.error = AppError.network(.connectionFailure)
            }
        case .noData:
            ToastManager.shared.showError(
                "No Response",
                message: "Server didn't return any data"
            )
            self.error = AppError.network(.invalidResponse)
        case .decodingError:
            ToastManager.shared.showError(
                "Invalid Response",
                message: "Couldn't parse server response"
            )
            self.error = AppError.network(.invalidResponse)
        case .noSessionId:
            ToastManager.shared.showError(
                "Session Error",
                message: "Please restart the app and try again"
            )
            self.error = AppError.network(.connectionFailure)
        case .sessionRefreshFailed:
            ToastManager.shared.showError(
                "Session Refresh Failed",
                message: "Please restart the app and try again"
            )
            self.error = AppError.network(.connectionFailure)
        default:
            ToastManager.shared.showError(
                "Unknown Error",
                message: apiError.localizedDescription
            )
            self.error = AppError.network(.serverError(-1))
        }
    }

    private func processInventoryResponse(_ response: InventoryParsingService.ServerResponse)
        -> [Ingredient]?
    {
        let inventory = response.results.compactMap { $0.ingredients_list?.inventory }.flatMap {
            $0
        }

        guard !inventory.isEmpty else {
            error = AppError.parsing(.emptyResponse)
            ToastManager.shared.showWarning(
                "No Ingredients Found",
                message: "Try taking clearer photos or include more visible ingredients",
                duration: 4.0
            )
            return nil
        }

        return inventory
    }

    private func clearImages() {
        thumbnails.removeAll()
        highResImages.removeAll()

        // Clear persisted images
        if let files = try? FileManager.default.contentsOfDirectory(
            at: imagesDirectory, includingPropertiesForKeys: nil)
        {
            for file in files {
                try? FileManager.default.removeItem(at: file)
            }
        }
    }

    func removeThumbnail(at index: Int) {
        guard thumbnails.indices.contains(index) else { return }
        thumbnails.remove(at: index)
        // delete highResImages also
        if highResImages.indices.contains(index) {
            // Delete from disk - we need to find the corresponding file
            // Since we don't store filenames, we'll delete by index order
            if let files = try? FileManager.default.contentsOfDirectory(
                at: imagesDirectory,
                includingPropertiesForKeys: [.creationDateKey],
                options: .skipsHiddenFiles)
            {
                let sortedFiles = files.sorted { file1, file2 in
                    let date1 =
                        (try? file1.resourceValues(forKeys: [.creationDateKey]))?.creationDate
                        ?? Date.distantPast
                    let date2 =
                        (try? file2.resourceValues(forKeys: [.creationDateKey]))?.creationDate
                        ?? Date.distantPast
                    return date1 > date2
                }
                if sortedFiles.indices.contains(index) {
                    try? FileManager.default.removeItem(at: sortedFiles[index])
                }
            }
            highResImages.remove(at: index)
        }
    }

    func setFPS(_ fps: Double) {
        cameraManager.setFPS(fps)
    }

    func startCamera() {
        cameraManager.startSession()
    }

    func stopCamera() {
        cameraManager.stopSession()
    }
}
