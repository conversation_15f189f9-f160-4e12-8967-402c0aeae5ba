import UIKit

// MARK: - Thumbnail Utility
extension UIImage {
    /// Returns a resized thumbnail with the given maximum dimension (width or height)
    func resizedThumbnail(maxDimension: CGFloat) -> UIImage {
        let aspect = size.width / size.height
        let newSize: CGSize
        if aspect > 1 {
            newSize = CGSize(width: maxDimension, height: maxDimension / aspect)
        } else {
            newSize = CGSize(width: maxDimension * aspect, height: maxDimension)
        }
        let renderer = UIGraphicsImageRenderer(size: newSize)
        return renderer.image { _ in
            self.draw(in: CGRect(origin: .zero, size: newSize))
        }
    }

    /// Returns a scaled thumbnail of the specified height (aspect ratio preserved)
    func scaledThumbnail(height: CGFloat) -> UIImage {
        let aspect = size.width / size.height
        let newSize = CGSize(width: height * aspect, height: height)
        UIGraphicsBeginImageContextWithOptions(newSize, false, 0.0)
        self.draw(in: CGRect(origin: .zero, size: newSize))
        let result = UIGraphicsGetImageFromCurrentImageContext() ?? self
        UIGraphicsEndImageContext()
        return result
    }

    /// Resize to a square (for low-res 512x512) - MIGHT NOT USE THIS - OPENAI SPEC
    func resizedToSquare512() -> UIImage? {
        let size = CGSize(width: 512, height: 512)
        UIGraphicsBeginImageContextWithOptions(size, false, 0.0) 
        self.draw(in: CGRect(origin: .zero, size: size))
        let resized = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return resized
    }

    /// JPEG Data with size check (default quality 0.9, under 20MB)
    func jpegDataIfUnder20MB(quality: CGFloat = 0.9) -> Data? {
        guard let data = self.jpegData(compressionQuality: quality), data.count < 20 * 1024 * 1024 else {
            return nil
        }
        return data
    }
}

// MARK: - Keyboard Dismiss Helper
extension UIApplication {
    func endEditing() {
        sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
}

// MARK: - Float Extensions
extension Float {
    // Clean float to string
    var clean: String {
        return self.truncatingRemainder(dividingBy: 1) == 0 ? String(format: "%.0f", self) : String(self)
    }
}