//
//  roastApp.swift
//  roast
//
//  Created by <PERSON> on 4/15/25.
//

import PostHog
import Swift<PERSON>

@main
struct roastApp: App {
    @UIApplicationDelegateAdaptor(AppDelegate.self) var appDelegate
    // @StateObject private var sessionManager = SessionManager.shared
    @StateObject private var appDataVM = AppDataViewModel()
    @StateObject private var asyncProcessingService = AsyncProcessingService.shared
    @StateObject private var pushNotificationService = PushNotificationService.shared

    init() {
        // SessionManager.shared.clearSession()

        let POSTHOG_POST = "https://us.i.posthog.com"
        if let POSTHOG_API_KEY = Bundle.main.object(forInfoDictionaryKey: "POSTHOG_API_KEY")
            as? String
        {
            // print("PostHog API Key: \(POSTHOG_API_KEY)")
            let config = PostHogConfig(apiKey: POSTHOG_API_KEY, host: POSTHOG_POST)
            PostHogSDK.shared.setup(config)
        }

        // Request push notification permissions on app launch
        Task {
            await PushNotificationService.shared.requestAuthorization()
        }

        // ensure session is established before app starts
        SessionManager.shared.ensureSession { sessionId in
            if let sessionId = sessionId {
                print("App Init: Session established/verified. ID: \(sessionId.prefix(4))...")
            } else {
                print("App Init: Failed to establish session.")
                // handle error. show error, prevent app use...
            }
        }
    }

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(appDataVM)
                .environmentObject(asyncProcessingService)
                .environmentObject(pushNotificationService)
        }
    }
}
