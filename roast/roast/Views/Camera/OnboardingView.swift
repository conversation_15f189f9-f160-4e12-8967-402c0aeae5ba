import SwiftUI

// MARK: - OnboardingView
struct OnboardingView: View {
    @Environment(\.colorScheme) var colorScheme
    @Binding var isPresented: Bool
    @Binding var detent: PresentationDetent

    var body: some View {
        // Overall VStack for the page content
        VStack(spacing: 24) {  // Reduced outer spacing from default/24
            // This Spacer pushes content down from the top edge of the sheet,
            // helping to vertically center content if it's short.
            Spacer()

            Image("cookAgent")  // Assuming you have this image in your assets
                .resizable()
                .scaledToFit()
                .frame(width: 80, height: 80)
                .padding(.bottom, 8)

            Text(detent == .large ? "CookAgent" : "Welcome to CookAgent")
                .font(.largeTitle)
                .fontWeight(.bold)
                .multilineTextAlignment(.center)
                // Added padding to ensure consistent spacing from elements above/below
                .padding(.bottom, 4) // detent == .large ? 8 : 4)

            // Group for detent-specific content, with animation
            Group {
                if detent == .large {
                    VStack(spacing: 20) {  // Consistent spacing for this section's elements
                        Text(
                            "Your AI-powered cooking assistant that helps you discover recipes based on the ingredients you have."
                        )
                        .font(.title3)
                        .multilineTextAlignment(.center)
                        .foregroundColor(.secondary)
                        .padding(.horizontal)
                        // .padding(.bottom, 12) // Space after description

                        // Group the steps for slightly tighter spacing if desired
                        VStack(alignment: .leading, spacing: 12) {
                            Text(
                                "1. Take photos of your groceries, pantry, or fridge."
                            )
                            Text(
                                "2. Select the ingredients you're interested in using"
                            )
                            Text(
                                "3. Let CookAgent suggest recipes you can make!"
                            )
                        }
                        .font(.body)
                        .multilineTextAlignment(.center)
                        .foregroundColor(.secondary)
                        .padding(.horizontal)

                        Spacer()  // Pushes the button to the bottom of this VStack's allocated space

                        Button("Get Started") {
                            isPresented = false
                        }
                        .buttonStyle(.borderedProminent)
                        .foregroundStyle(colorScheme == .dark ? .black : .white)
                        .controlSize(.large)
                        .padding(.bottom, 20)  // Reduced bottom padding for the button
                        .transition(.move(edge: .bottom).combined(with: .opacity))
                    }
                    .transition(.opacity.animation(.easeInOut))  // Transition for the whole block

                } else {  // Medium Detent
                    VStack(spacing: 16) {  // Consistent spacing for this section
                        Text("Get recipe suggestions from your ingredients.")
                            .font(.body)
                            .multilineTextAlignment(.center)
                            .foregroundColor(.primary)  // As per original
                            .padding(.horizontal)

                        Spacer()  // This is important to push the "Swipe up" text down

                        Text("Swipe up for more information")
                            .font(.callout)
                            .foregroundColor(.secondary)
                            .padding(.horizontal)
                            .padding(.bottom, 10)  // Add some padding so it's not glued to bottom
                    }
                    .transition(.opacity.animation(.easeInOut))  // Transition for the whole block
                }
            }
            // Animate changes within the Group based on detent.
            // Applying animation here to the group is more standard than on individual transitions.
            .animation(.easeInOut, value: detent)

            // No final spacer here, as the Spacer() at the top and internal Spacers
            // manage the vertical distribution.
        }
        .padding()  // Overall padding from sheet edges (sides, and also top/bottom if content is short)
        .frame(maxHeight: .infinity, alignment: .top)  // VStack content aligns top, initial Spacer pushes it down.
        .navigationTitle("")
        .navigationBarTitleDisplayMode(.inline)
        // Original .toolbar was commented out, so kept it that way.
        // .toolbar {
        //     ToolbarItem(placement: .navigationBarTrailing) {
        //         Button("Done") {
        //             isPresented = false
        //         }
        //     }
        // }
        // It's often better to apply animations closer to the views that change.
        // The .animation(.easeInOut, value: detent) on the Group is good.
        // If further animation control is needed for the whole sheet, you could add one here too.
    }
}

// Keep your CameraView_Previews and other structs as they are.
// Example Preview struct (if your OnboardingView is in a separate file):
struct OnboardingView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            OnboardingView(isPresented: .constant(true), detent: .constant(.large))
                .previewDisplayName("Large Detent")

            OnboardingView(isPresented: .constant(true), detent: .constant(.medium))
                .previewDisplayName("Medium Detent")
        }
    }
}
