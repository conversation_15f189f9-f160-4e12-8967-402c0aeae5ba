import SwiftUI

struct IdentifiableInt: Identifiable, Equatable {
    let id: Int
}

struct ThumbnailRowView: View {
    @ObservedObject var camVM: CameraViewModel
    @State private var space_padding: CGFloat = 12
    @State private var expandedIndex: IdentifiableInt? = nil
    @State private var lastDismissedIndex: Int? = nil

    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(alignment: .center, spacing: space_padding) {
                ForEach(camVM.thumbnails.indices, id: \.self) { idx in
                    ZStack(alignment: .topTrailing) {
                        // Image is always present, but may be blank if not loaded
                        if let image = camVM.thumbnails[idx].image {
                            Image(uiImage: image)
                                .resizable()
                                .aspectRatio(9/16, contentMode: .fill)
                                .frame(width: camVM.thumbnailWidth, height: camVM.thumbnailHeight)
                                .clipShape(RoundedRectangle(cornerRadius: 8, style: .continuous))
                                .onTapGesture {
                                    expandedIndex = IdentifiableInt(id: idx)
                                }
                                .accessibilityLabel("Expand thumbnail")
                        }
                        // Delete button is above the image but below the overlay
                        if !camVM.thumbnails[idx].isLoading {
                            Button(
                                action: { camVM.removeThumbnail(at: idx) }
                            ) {
                                Image(systemName: "xmark.circle.fill")
                                    .frame(width: 20, height: 20)
                                    .foregroundColor(.white)
                                    .background(Circle().fill(Color.black.opacity(0.65)))
                            }
                            .padding(4)
                        }
                        // Gray overlay fades out when image is loaded
                        RoundedRectangle(cornerRadius: 8, style: .continuous)
                            .fill(Color(white: 0.1))
                            .frame(width: camVM.thumbnailWidth, height: camVM.thumbnailHeight)
                            .opacity(camVM.thumbnails[idx].isLoading ? 1 : 0)
                            .animation(.easeIn(duration: 0.3), value: camVM.thumbnails[idx].isLoading)
                            .allowsHitTesting(camVM.thumbnails[idx].isLoading)
                        // Border always on top
                        RoundedRectangle(cornerRadius: 8, style: .continuous)
                            .stroke(Color.white, lineWidth: 2)
                    }
                    .padding(.vertical, 8)

                }
            }
            .padding(.horizontal, space_padding)
        }
        .frame(height: camVM.thumbnailHeight)
        // Expanded image sheet
        .sheet(item: $expandedIndex, onDismiss: {
            if let id = lastDismissedIndex {
                camVM.removeThumbnail(at: id)
                lastDismissedIndex = nil
            }
        }) { item in
            // Prefer high-res image if available, else fallback to thumbnail
            let highResImage = camVM.highResImages.indices.contains(item.id) ? camVM.highResImages[item.id] : nil
            let displayImage = highResImage ?? camVM.thumbnails[item.id].image
            if let image = displayImage {
                ZStack(alignment: .topTrailing) {
                    HStack {
                        Button(action: {
                            expandedIndex = nil
                        }) {
                            Image(systemName: "xmark")
                                .font(.system(size: 22))
                                .foregroundColor(.primary)
                                .padding()
                        }
                        Spacer()
                        Button(action: {
                            lastDismissedIndex = item.id
                            expandedIndex = nil
                        }) {
                            Image(systemName: "trash")
                                .font(.system(size: 22))
                                .foregroundColor(.primary)
                                .padding()
                        }
                    }
                    VStack {
                        Spacer()
                        Image(uiImage: image)
                            .resizable()
                            .scaledToFit()
                            .cornerRadius(8)
                            .padding()
                        Spacer()
                    }
                }
            }
        }
    }
}
