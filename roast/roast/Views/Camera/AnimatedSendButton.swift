import SwiftUI

// MARK: - Animated Send But<PERSON> (Updated to use RoundButton)
struct AnimatedSendButton: View {
    var isVisible: Bool
    var action: () -> Void
    var size: CGFloat
    
    var body: some View {
        RoundButton("paperplane", action: action)
            .visible(isVisible)
            .size(size)
            .iconSize(size * 0.4) // Scale icon size relative to button size
            .style(.glassmorphic)
            .animateEntrance(true)
    }
}

// MARK: - Unanimated Send Button (Updated to use RoundButton)
struct SendButton: View {
    var isEnabled: Bool
    var action: () -> Void
    
    var body: some View {
        RoundButton("arrow.up.circle.fill", action: action)
            .enabled(isEnabled)
            .size(50)
            .iconSize(30)
            .style(.solid)
            .backgroundColor(.clear)
            .foregroundColor(isEnabled ? .white.opacity(0.78) : .white.opacity(0.28))
            .animateEntrance(false)
    }
}
