import SwiftUI

struct CameraView: View {
	@EnvironmentObject var appDataVM: AppDataViewModel
	@EnvironmentObject var asyncProcessingService: AsyncProcessingService
	@Binding var path: [AppFlowStep]
	@ObservedObject var viewModel: CameraViewModel

	@ScaledMetric private var captureButtonSize: CGFloat = 74
	@ScaledMetric private var sendButtonSize: CGFloat = 60
	@ScaledMetric private var helpButtonSize: CGFloat = 54
	@ScaledMetric private var iconSize: CGFloat = 24

	@State private var showSendConfirmation = false
	@State private var showOnboarding = false
	@State private var onboardingDetent: PresentationDetent = .medium
	@State private var showSettings = false

	@AppStorage("hasSeenOnboarding") private var hasSeenOnboarding = false
	@AppStorage("useAsyncProcessing") private var useAsyncProcessing = true // Boolean toggle

	@StateObject private var loadingManager = LoadingStateManager()
	
	// Notification observers
	@State private var asyncIngredientsObserver: NSObjectProtocol?
	@State private var navigationObserver: NSObjectProtocol?

	init(path: Binding<[AppFlowStep]>, viewModel: CameraViewModel) {
		self._path = path
		self.viewModel = viewModel
	}

	private func handleCaptureButtonTouchDown() {
		viewModel.setFPS(30)
		withAnimation(.easeInOut(duration: 0.15)) {
			viewModel.overlayOpacity = 0.0
		}
	}

	private func handleCaptureButtonTouchUp() {
		withAnimation(.easeInOut(duration: 0.15)) {
			viewModel.overlayOpacity = 1.0
		}
		viewModel.handleCapture()
	}

	private func handleCaptureButtonTouchUpCancel() {
		viewModel.setFPS(15)
		withAnimation(.easeInOut(duration: 0.15)) {
			viewModel.overlayOpacity = 1.0
		}
	}

	private func handleCaptureButtonLongPress() {
		viewModel.setFPS(30)
	}

	// Breaking down complex view into smaller components for better type checking
	private var helpButtonView: some View {
		HStack {
			Spacer()
				.frame(maxWidth: .infinity)
			// MARK: - Help Button
			RoundButton("questionmark.circle") {
				let impactFeedback = UIImpactFeedbackGenerator(style: .light)
				impactFeedback.impactOccurred()
				showOnboarding = true
			}
			.size(helpButtonSize)
			.iconSize(iconSize)
			.animateEntrance(false)
			.accessibilityLabel("Show onboarding")
			.accessibilityHint("Opens a guide explaining how to use CookAgent")
			
			// MARK: - Settings Button
			RoundButton("gearshape") {
				let impactFeedback = UIImpactFeedbackGenerator(style: .light)
				impactFeedback.impactOccurred()
				showSettings = true
			}
			.size(helpButtonSize)
			.iconSize(iconSize)
			.animateEntrance(false)
			.accessibilityLabel("Show settings")
			.accessibilityHint("Opens app settings and processing options")

			Spacer()
				.frame(maxWidth: .infinity)
		}
		.frame(maxWidth: .infinity)
	}

	private var captureButtonView: some View {
		Group {
			if viewModel.isSpinnerLoading {
				SpinnerCircle(isLoading: $viewModel.isSpinnerLoading)
					.frame(width: captureButtonSize, height: captureButtonSize)
					.transition(.opacity)
			} else {
				CaptureButton(
					isPressed: $viewModel.isCaptureButtonPressed,
					onTouchDown: handleCaptureButtonTouchDown,
					onTouchUp: handleCaptureButtonTouchUp,
					onTouchUpCancel: handleCaptureButtonTouchUpCancel,
					onLongPress: handleCaptureButtonLongPress,
					size: captureButtonSize
				)
				.scaleEffect(viewModel.isCaptureButtonPressed ? 0.93 : 1.0)
				.animation(
					.spring(response: 0.23, dampingFraction: 0.7),
					value: viewModel.isCaptureButtonPressed
				)
				.transition(.opacity)
			}
		}
	}

	private var sendButtonView: some View {
		HStack {
			Spacer()
				.frame(maxWidth: .infinity)
			// MARK: - Send Images Button
			let allThumbnailsLoaded = viewModel.thumbnails.allSatisfy { !$0.isLoading }
			let hasContent = !viewModel.thumbnails.isEmpty
			RoundButton("paperplane") {
				showSendConfirmation = true
			}
			.visible(allThumbnailsLoaded && hasContent)
			.size(sendButtonSize)
			.iconSize(iconSize)
			.style(.glassmorphic)
			.foregroundColor(.primary)
			.animateEntrance(true)

			Spacer()
				.frame(maxWidth: .infinity)
		}
		.frame(maxWidth: .infinity)
	}

	private var captureControls: some View {
		HStack(alignment: .center) {
			helpButtonView
			captureButtonView
			sendButtonView
		}
	}

	// Extract navigation button views to improve type-checking
	@ViewBuilder
	private var processingIndicatorView: some View {
		let processingCount = asyncProcessingService.pendingRequests.filter { $0.status == .processing }.count
		
		Button(action: {
			// Could navigate to a processing status view
		}) {
			HStack(spacing: 8) {
				ProgressView()
					.scaleEffect(0.8)
				Text("Processing")
					.font(.subheadline)
					.fontWeight(.medium)
				Text("\(processingCount)")
					.font(.caption)
					.foregroundColor(.secondary)
			}
			.foregroundColor(.primary)
			.padding(.horizontal, 16)
			.padding(.vertical, 10)
			.background(VisualEffectBlur(blurStyle: .systemUltraThinMaterial))
			.cornerRadius(20)
		}
		.disabled(true) // Just an indicator for now
		.accessibilityLabel("Processing requests")
		.accessibilityHint("\(processingCount) ingredient analysis in progress")
	}

	@ViewBuilder
	private func ingredientButtonView(_ count: Int) -> some View {
		Button(action: {
			let impactFeedback = UIImpactFeedbackGenerator(style: .light)
			impactFeedback.impactOccurred()
			path.append(.ingredients)
		}) {
			HStack(spacing: 8) {
				Image(systemName: "carrot")
					.font(.system(size: 16, weight: .medium))
				Text("Ingredients")
					.font(.subheadline)
					.fontWeight(.medium)
				Text("\(count)")
					.font(.caption)
					.foregroundColor(.secondary)
			}
			.foregroundColor(.primary)
			.padding(.horizontal, 16)
			.padding(.vertical, 10)
			.background(VisualEffectBlur(blurStyle: .systemUltraThinMaterial))
			.cornerRadius(20)
		}
		.accessibilityLabel("View ingredients")
		.accessibilityHint("Shows \(count) saved ingredients")
	}

	@ViewBuilder
	private func recipeButtonView(_ count: Int) -> some View {
		Button(action: {
			let impactFeedback = UIImpactFeedbackGenerator(style: .light)
			impactFeedback.impactOccurred()
			path.append(.savedRecipes)
		}) {
			HStack(spacing: 8) {
				Image(systemName: "text.word.spacing")
					.font(.system(size: 16, weight: .medium))
				Text("Recipes")
					.font(.subheadline)
					.fontWeight(.medium)
				Text("\(count)")
					.font(.caption)
					.foregroundColor(.secondary)
			}
			.foregroundColor(.primary)
			.padding(.horizontal, 16)
			.padding(.vertical, 12)
			.background(VisualEffectBlur(blurStyle: .systemUltraThinMaterial))
			.cornerRadius(20)
		}
		.accessibilityLabel("View saved recipes")
		.accessibilityHint("Shows \(count) saved recipes")
	}

	@ViewBuilder
	private func navigationButtonsView(hasIngredients: Bool, hasRecipes: Bool) -> some View {
		if hasIngredients || hasRecipes || !asyncProcessingService.pendingRequests.isEmpty {
			VStack(spacing: 12) {
				HStack(spacing: 16) {
					// Processing indicator
					if !asyncProcessingService.pendingRequests.isEmpty {
						processingIndicatorView
					}

					if hasIngredients {
						ingredientButtonView(appDataVM.inventory.count)
					}

					if hasRecipes {
						recipeButtonView(appDataVM.storedRecipes.count)
					}
				}
			}
			.padding(.bottom, 8)
		}
	}

	// Main content layer with camera and overlays
	private var mainContentLayer: some View {
		ZStack {
			// Response loading layer
			if viewModel.isWaitingForServerResponse {
				ResponseLoadingView()
					.transition(.opacity)
					.zIndex(10)
			}

			// MARK: - Camera Preview
			if path.isEmpty {
				CameraPreview(session: viewModel.cameraManager.session)
					.ignoresSafeArea()
			}

			// Blur overlay
			VisualEffectBlur(blurStyle: .systemMaterial)
				.opacity(viewModel.overlayOpacity)
				.ignoresSafeArea()

			titleView
			controlElementsView
		}
	}

	// Title section
	private var titleView: some View {
		VStack {
			HStack {
				Text("CookAgent")
					.font(.largeTitle)
					.fontWeight(.bold)
					.foregroundColor(viewModel.overlayOpacity == 1.0 ? .primary : .secondary)
					.opacity(viewModel.overlayOpacity == 1.0 ? 1.0 : 0.7)
				
				// Ready results indicator
				if asyncProcessingService.hasDataReady {
					Button(action: {
						asyncProcessingService.fetchReadyResults()
					}) {
						HStack(spacing: 4) {
							Image(systemName: "bell.badge")
								.foregroundColor(.orange)
							Text("\(asyncProcessingService.readyRequestIds.count)")
								.font(.caption)
								.foregroundColor(.orange)
						}
						.padding(.horizontal, 8)
						.padding(.vertical, 4)
						.background(Color.orange.opacity(0.1))
						.cornerRadius(12)
					}
					.accessibilityLabel("Ready results")
					.accessibilityHint("Tap to fetch \(asyncProcessingService.readyRequestIds.count) ready result\(asyncProcessingService.readyRequestIds.count == 1 ? "" : "s")")
				}
				
				Spacer()
			}
			.padding(.top, 10)
			.padding(.leading, 15)
			.frame(maxWidth: .infinity, alignment: .leading)
			Spacer()
		}
	}

	// Control elements at the bottom
	private var controlElementsView: some View {
		VStack {
			Spacer()

			let hasIngredients = !appDataVM.inventory.isEmpty
			let hasRecipes = !appDataVM.storedRecipes.isEmpty
			navigationButtonsView(hasIngredients: hasIngredients, hasRecipes: hasRecipes)

			if !viewModel.thumbnails.isEmpty {
				ThumbnailRowView(camVM: viewModel)
			}

			let isThumbnailLoading = viewModel.thumbnails.first?.isLoading == true
			let _ = isThumbnailLoading
			captureControls
				.padding(.vertical)
				.onChange(of: isThumbnailLoading) { newValue in
					viewModel.isSpinnerLoading = newValue
				}
		}
		.ignoresSafeArea()
		.padding(.bottom, 10)
	}

	// All overlay views
	private var overlayViews: some View {
		Group {
			loadingOverlay
			holdToRevealOverlay
		}
	}

	// Loading overlay
	private var loadingOverlay: some View {
		Group {
			if loadingManager.isLoading {
				SimpleLoadingView(message: loadingManager.loadingMessage)
					.zIndex(3)
			}
		}
	}

	// Hold to reveal overlay
	private var holdToRevealOverlay: some View {
		VStack {
			Spacer()
			Text("Hold to reveal")
				.font(.caption)
				.opacity(viewModel.overlayOpacity == 1.0 ? 0.6 : viewModel.overlayOpacity)
				.padding(.bottom, 5)
		}
		.zIndex(1)
	}

	// View modifiers for the main view
	private var viewModifiers: some ViewModifier {
		struct CameraViewModifier: ViewModifier {
			let hasSeenOnboarding: Bool
			let showOnboarding: Binding<Bool>
			let path: Binding<[AppFlowStep]>
			let viewModel: CameraViewModel
			let appDataVM: AppDataViewModel

			func body(content: Content) -> some View {
				content
					.tint(.primary)
					.onAppear {
						print("CameraView appeared")
						if !hasSeenOnboarding {
							showOnboarding.wrappedValue = true
						}
						if path.wrappedValue.isEmpty {
							viewModel.startCamera()
						}
						viewModel.onSendSuccess = { inventory in
							appDataVM.inventory = inventory
							path.wrappedValue.append(.ingredients)
						}
					}
					.onDisappear {
						print("CameraView disappeared")
						viewModel.stopCamera()
					}
					.onChange(of: path.wrappedValue) { newPath in
						print("Step changed to \(newPath)")
						if newPath.isEmpty {
							viewModel.startCamera()
						} else {
							viewModel.stopCamera()
						}
					}
			}
		}

		return CameraViewModifier(
			hasSeenOnboarding: hasSeenOnboarding,
			showOnboarding: $showOnboarding,
			path: $path,
			viewModel: viewModel,
			appDataVM: appDataVM
		)
	}

	var body: some View {
		mainContentLayer
			.tint(.primary)
			.onAppear {
				print("CameraView appeared")
				if !hasSeenOnboarding {
					showOnboarding = true
					hasSeenOnboarding = true
				}
				if path.isEmpty {
					viewModel.startCamera()
				}
				viewModel.onSendSuccess = { inventory in
					appDataVM.inventory = inventory
					path.append(.ingredients)
				}
				
				// Set up async results listener
				asyncIngredientsObserver = NotificationCenter.default.addObserver(
					forName: NSNotification.Name("AsyncIngredientsReceived"),
					object: nil,
					queue: .main
				) { notification in
					if let ingredients = notification.userInfo?["ingredients"] as? [Ingredient] {
						appDataVM.inventory = ingredients
						path.append(.ingredients)
					}
				}
				
				// Navigation listener
				navigationObserver = NotificationCenter.default.addObserver(
					forName: NSNotification.Name("NavigateToIngredients"),
					object: nil,
					queue: .main
				) { _ in
					path.append(.ingredients)
				}
			}
				
			// Auto-fetch ready results when data is ready
			.onChange(of: asyncProcessingService.hasDataReady) { hasData in
				if hasData {
					print("[CameraView] Data ready detected, fetching results...")
					asyncProcessingService.fetchReadyResults()
				}
			}
			.onDisappear {
				print("CameraView disappeared")
				viewModel.stopCamera()
				
				// Remove observers
				if let asyncObserver = asyncIngredientsObserver {
					NotificationCenter.default.removeObserver(asyncObserver)
				}
				if let navObserver = navigationObserver {
					NotificationCenter.default.removeObserver(navObserver)
				}
			}
			.onChange(of: path) { newPath in
				print("Step changed to \(newPath)")
				if newPath.isEmpty {
					viewModel.startCamera()
				} else {
					viewModel.stopCamera()
				}
			}
			.sheet(isPresented: $viewModel.showShareSheet, onDismiss: { viewModel.exportURL = nil })
		{
			if let exportURL = viewModel.exportURL {
				ShareSheet(activityItems: [exportURL])
			}
		}
			.confirmationDialog(
				"Are you sure you want to submit these images?",
				isPresented: $showSendConfirmation,
				titleVisibility: .visible
			) {
				if useAsyncProcessing {
					Button("Submit for Processing") {
						print("[CameraView] Using async processing")
						viewModel.sendImagesAsync()
					}
				} else {
					Button("Generate List", role: .destructive) {
						print("[CameraView] Using sync processing")
						viewModel.isWaitingForServerResponse = true
						viewModel.sendImages()
					}
				}
				Button("Cancel", role: .cancel) {}
			} message: {
				Text(useAsyncProcessing ? 
					"You'll receive a push notification when processing is complete." :
					"Double check your images for clarity before submitting.")
			}
			.sheet(isPresented: $showOnboarding) {
				OnboardingView(isPresented: $showOnboarding, detent: $onboardingDetent)
					.presentationDetents([.medium, .large], selection: $onboardingDetent)
					.presentationDragIndicator(.visible)
			}
			.sheet(isPresented: $showSettings) {
				SettingsView()
			}
			.overlay(overlayViews)
	}
}

struct CameraView_Previews: PreviewProvider {
	static var previews: some View {
		let appDataVM = AppDataViewModel()
		let cameraVM = CameraViewModel(appDataVM: appDataVM)
		return CameraView(path: .constant([]), viewModel: cameraVM)
			.environmentObject(appDataVM)
	}
}
