import SwiftUI

struct CameraView: View {
    @EnvironmentObject var appDataVM: AppDataViewModel
    @Binding var path: [AppFlowStep]
    @ObservedObject var viewModel: CameraViewModel

    @ScaledMetric private var captureButtonSize: CGFloat = 74
    @ScaledMetric private var sendButtonSize: CGFloat = 54
    @ScaledMetric private var helpButtonSize: CGFloat = 44
    @State private var showSendConfirmation = false
    @State private var showOnboarding = false
    @AppStorage("hasSeenOnboarding") private var hasSeenOnboarding = false
    @StateObject private var loadingManager = LoadingStateManager()

    init(path: Binding<[AppFlowStep]>, viewModel: CameraViewModel) {
        self._path = path
        self.viewModel = viewModel
    }

    private func handleCaptureButtonTouchDown() {
        viewModel.setFPS(30)
        withAnimation(.easeInOut(duration: 0.15)) {
            viewModel.overlayOpacity = 0.0
        }
    }

    private func handleCaptureButtonTouchUp() {
        withAnimation(.easeInOut(duration: 0.15)) {
            viewModel.overlayOpacity = 1.0
        }
        viewModel.handleCapture()
    }

    private func handleCaptureButtonTouchUpCancel() {
        viewModel.setFPS(15)
        withAnimation(.easeInOut(duration: 0.15)) {
            viewModel.overlayOpacity = 1.0
        }
    }

    private func handleCaptureButtonLongPress() {
        viewModel.setFPS(30)
    }

    private var captureControls: some View {
        HStack(alignment: .center) {
            HStack {
                Spacer()
                    .frame(maxWidth: .infinity)
                // MARK: - Help Button
                Button(action: {
                    let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                    impactFeedback.impactOccurred()
                    showOnboarding = true
                }) {
                    Image(systemName: "questionmark.circle")
                        .font(.system(size: helpButtonSize * 0.6, weight: .medium))
                        .foregroundColor(.white)
                        .opacity(0.8)
                }
                .frame(width: helpButtonSize, height: helpButtonSize)
                .accessibilityLabel("Show onboarding")
                .accessibilityHint("Opens a guide explaining how to use CookAgent")
                Spacer()
                    .frame(maxWidth: .infinity)
            }
            .frame(maxWidth: .infinity)
            // MARK: - Capture Button
            if viewModel.isSpinnerLoading {
                SpinnerCircle(isLoading: $viewModel.isSpinnerLoading)
                    .frame(width: captureButtonSize, height: captureButtonSize)
                    .transition(.opacity)
            } else {
                ZStack {
                    CaptureButton(
                        isPressed: $viewModel.isCaptureButtonPressed,
                        onTouchDown: handleCaptureButtonTouchDown,
                        onTouchUp: handleCaptureButtonTouchUp,
                        onTouchUpCancel: handleCaptureButtonTouchUpCancel,
                        onLongPress: handleCaptureButtonLongPress,
                        size: captureButtonSize
                    )
                    .scaleEffect(viewModel.isCaptureButtonPressed ? 0.93 : 1.0)
                    .animation(
                        .spring(response: 0.23, dampingFraction: 0.7),
                        value: viewModel.isCaptureButtonPressed
                    )
                    .transition(.opacity)
                    
                    CaptureButtonOnboarding(
                                isShowing: $showOnboarding,
                                buttonSize: 80,
                                buttonPosition: CGPoint(x: 0, y: 0)
                            )
                }
            }

            HStack {
                Spacer()
                    .frame(maxWidth: .infinity)
                // MARK: - Send Images Button
                let allThumbnailsLoaded = viewModel.thumbnails.allSatisfy { !$0.isLoading }
                let hasContent = !viewModel.thumbnails.isEmpty
                AnimatedSendButton(
                    isVisible: allThumbnailsLoaded && hasContent,
                    action: {
                        showSendConfirmation = true
                    },
                    size: sendButtonSize
                )
                Spacer()
                    .frame(maxWidth: .infinity)
            }
            .frame(maxWidth: .infinity)
        }
    }

    var body: some View {
        ZStack {
            if viewModel.isWaitingForServerResponse {
                ResponseLoadingView()
                    .transition(.opacity)
                    .zIndex(10)
            }

            // MARK: - Camera Preview
            if path.isEmpty {
                CameraPreview(session: viewModel.cameraManager.session)
                    .ignoresSafeArea()
            }
            VisualEffectBlur(blurStyle: .systemMaterial)
                .opacity(viewModel.overlayOpacity)
                .ignoresSafeArea()
            // MARK: - Control Elements
            VStack {
                // Spacer to move content to bottom
                Spacer()

                // Navigation buttons for existing content
                let hasIngredients = !appDataVM.inventory.isEmpty
                let hasRecipes = !appDataVM.storedRecipes.isEmpty

                if hasIngredients || hasRecipes {
                    VStack(spacing: 12) {
                        HStack(spacing: 16) {
                            // Ingredients button
                            if hasIngredients {
                                Button(action: {
                                    let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                                    impactFeedback.impactOccurred()
                                    path.append(.ingredients)
                                }) {
                                    HStack(spacing: 8) {
                                        Image(systemName: "carrot.fill")
                                            .font(.system(size: 16, weight: .medium))
                                        Text("Ingredients")
                                            .font(.subheadline)
                                            .fontWeight(.medium)
                                        Text("\(appDataVM.inventory.count)")
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                    }
                                    .foregroundColor(.primary)
                                    .padding(.horizontal, 16)
                                    .padding(.vertical, 12)
                                    .background(
                                        VisualEffectBlur(blurStyle: .systemUltraThinMaterial)
                                    )
                                    .cornerRadius(20)
                                }
                                .accessibilityLabel("View ingredients")
                                .accessibilityHint(
                                    "Shows \(appDataVM.inventory.count) saved ingredients")
                            }

                            // Saved Recipes button
                            if hasRecipes {
                                Button(action: {
                                    let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                                    impactFeedback.impactOccurred()
                                    path.append(.savedRecipes)
                                }) {
                                    HStack(spacing: 8) {
                                        Image(systemName: "book.fill")
                                            .font(.system(size: 16, weight: .medium))
                                        Text("Recipes")
                                            .font(.subheadline)
                                            .fontWeight(.medium)
                                        Text("\(appDataVM.storedRecipes.count)")
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                    }
                                    .foregroundColor(.primary)
                                    .padding(.horizontal, 16)
                                    .padding(.vertical, 12)
                                    .background(
                                        VisualEffectBlur(blurStyle: .systemUltraThinMaterial)
                                    )
                                    .cornerRadius(20)
                                }
                                .accessibilityLabel("View saved recipes")
                                .accessibilityHint(
                                    "Shows \(appDataVM.storedRecipes.count) saved recipes")
                            }
                        }
                    }
                    .padding(.bottom, 8)
                }
                // MARK: - Thumbnail Row
                if !viewModel.thumbnails.isEmpty {
                    ThumbnailRowView(camVM: viewModel)
                }
                let isThumbnailLoading = viewModel.thumbnails.first?.isLoading == true
                captureControls  // Use the new computed property here
                    .padding(.vertical)
                    .onChange(of: isThumbnailLoading) { newValue in
                        viewModel.isSpinnerLoading = newValue
                    }
            }
            .ignoresSafeArea()
            .padding(.bottom, 10)
        }
        .tint(.primary)
        // Set up callback to update appDataVM and trigger navigation on send success
        .onAppear {
            print("CameraView appeared")
            if !hasSeenOnboarding {
                showOnboarding = true
                hasSeenOnboarding = true
            }
            if path.isEmpty {
                viewModel.startCamera()
            }
            viewModel.onSendSuccess = { inventory in
                appDataVM.inventory = inventory
                path.append(.ingredients)
            }
        }
        .onDisappear {
            print("CameraView disappeared")
            viewModel.stopCamera()
            viewModel.highResImages.removeAll()
            viewModel.thumbnails.removeAll()
        }
        // Observe navigation step and manage camera lifecycle
        // .onChange(of: path) { oldPath, newPath in
        .onChange(of: path) { newPath in
            print("Step changed to \(newPath)")
            if newPath.isEmpty {
                viewModel.startCamera()
            } else {
                viewModel.stopCamera()
            }
        }
        // Share sheet presentation
        .sheet(isPresented: $viewModel.showShareSheet, onDismiss: { viewModel.exportURL = nil }) {
            if let exportURL = viewModel.exportURL {
                ShareSheet(activityItems: [exportURL])
            }
        }
        .confirmationDialog(
            "Are you sure you want to submit these images?",
            isPresented: $showSendConfirmation,
            titleVisibility: .visible
        ) {
            Button("Generate List", role: .destructive) {
                viewModel.isWaitingForServerResponse = true
                viewModel.sendImages()
            }
            Button("Cancel", role: .cancel) {}
        } message: {
            Text("Double check your images for clarity before submitting.")
        }
        .sheet(isPresented: $showOnboarding) {
            OnboardingView(isPresented: $showOnboarding)
                .presentationDetents([.medium, .large])
                .presentationDragIndicator(.visible)
        }
        .overlay(
            // Loading overlay
            Group {
                if loadingManager.isLoading {
                    SimpleLoadingView(message: loadingManager.loadingMessage)
                        .zIndex(1000)
                }
            }
        )
        .overlay(
            // Toast overlay
            Group {
                if loadingManager.showToast {
                    ToastView(
                        message: loadingManager.toastMessage,
                        type: loadingManager.toastType,
                        isShowing: $loadingManager.showToast
                    )
                }
            }
        )
    }
}

// MARK: - OnboardingView
struct OnboardingView: View {
    @Binding var isPresented: Bool

    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                Spacer()

                Text("👨‍🍳")
                    .font(.system(size: 80))

                Text("Welcome to CookAgent!")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .multilineTextAlignment(.center)

                Text(
                    "Your AI-powered cooking assistant that helps you discover amazing recipes based on the ingredients you have."
                )
                .font(.title3)
                .multilineTextAlignment(.center)
                .foregroundColor(.secondary)
                .padding(.horizontal)

                Text(
                    "Simply take photos of your ingredients and let CookAgent suggest delicious recipes you can make!"
                )
                .font(.body)
                .multilineTextAlignment(.center)
                .foregroundColor(.secondary)
                .padding(.horizontal)

                Spacer()

                Button("Get Started") {
                    isPresented = false
                }
                .buttonStyle(.borderedProminent)
                .controlSize(.large)
                .padding(.bottom, 32)
            }
            .padding()
            .navigationTitle("")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        isPresented = false
                    }
                }
            }
        }
    }
}

struct CameraView_Previews: PreviewProvider {
    static var previews: some View {
        let appDataVM = AppDataViewModel()
        let cameraVM = CameraViewModel(appDataVM: appDataVM)
        return CameraView(path: .constant([]), viewModel: cameraVM)
            .environmentObject(appDataVM)
    }
}
