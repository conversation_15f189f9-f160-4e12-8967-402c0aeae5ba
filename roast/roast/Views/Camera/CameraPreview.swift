import SwiftUI
import UIKit
import AVFoundation

struct CameraPreview: UIViewControllerRepresentable {
    let session: AVCaptureSession

    func makeUIViewController(context: Context) -> UIViewController {
        let controller = UIViewController()
        let previewLayer = AVCaptureVideoPreviewLayer(session: session)
        previewLayer.videoGravity = .resizeAspectFill
        previewLayer.frame = UIScreen.main.bounds
        controller.view.layer.addSublayer(previewLayer)
        context.coordinator.previewLayer = previewLayer
        return controller
    }

    func updateUIViewController(_ uiViewController: UIViewController, context: Context) {
        if let previewLayer = context.coordinator.previewLayer {
            previewLayer.frame = uiViewController.view.bounds
        }
    }

    func makeCoordinator() -> Coordinator {
        Coordinator()
    }

    class Coordinator {
        var previewLayer: AVCaptureVideoPreviewLayer?
    }
}

