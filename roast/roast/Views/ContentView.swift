import SwiftUI

struct ContentView: View {
    // @StateObject private var appDataVM = AppDataViewModel()
    // @EnvironmentObject private var appDataVM: AppDataViewModel
    @StateObject private var coordinator = AppCoordinator()
    // @StateObject private var recipesVM: RecipesViewModel
    @State private var path: [AppFlowStep] = []

    var body: some View {
        ZStack {
            NavigationStack(path: $path) {
                CameraView(path: $path, viewModel: coordinator.cameraVM)
                    .navigationDestination(for: AppFlowStep.self) { step in
                        switch step {
                        case .ingredients:
                            IngredientsView(path: $path, viewModel: coordinator.ingredientsVM)
                        case .recipes:
                            RecipesView(path: $path, viewModel: coordinator.recipesVM)
                        case .recipe(let recipeId):
                            if let recipeSeed = coordinator.appDataVM.recipeSeeds.first(where: {
                                $0.id == recipeId
                            }) {
                                RecipeDetailView(
                                    viewModel: coordinator.recipesVM, recipeSeed: recipeSeed)
                            } else {
                                Text("Recipe not found.")
                            }
                        case .savedRecipes:
                            SavedRecipesView(path: $path)
                        default:
                            EmptyView()
                        }
                    }
            }
            .tint(.primary)
            .environmentObject(coordinator.appDataVM)
            
            // Toast overlay
            ToastOverlay()
        }
    }
}

struct ContentView_Previews: PreviewProvider {
    static var previews: some View {
        ContentView()
    }
}
