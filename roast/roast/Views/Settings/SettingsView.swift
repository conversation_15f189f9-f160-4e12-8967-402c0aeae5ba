import SwiftUI

struct SettingsView: View {
    @AppStorage("useAsyncProcessing") private var useAsyncProcessing = true
    @EnvironmentObject var pushNotificationService: PushNotificationService
    @EnvironmentObject var asyncProcessingService: AsyncProcessingService
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            Form {
                Section("Processing Mode") {
                    VStack(alignment: .leading, spacing: 8) {
                        Toggle("Asynchronous Processing", isOn: $useAsyncProcessing)
                        
                        Text(useAsyncProcessing ? 
                             "Submit images and get notified when processing is complete. Allows you to continue using the app." :
                             "Wait for immediate results. Processing happens in real-time but takes longer.")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Section("Push Notifications") {
                    HStack {
                        Text("Status")
                        Spacer()
                        Text(pushNotificationService.isAuthorized ? "Enabled" : "Disabled")
                            .foregroundColor(pushNotificationService.isAuthorized ? .green : .orange)
                    }
                    
                    if !pushNotificationService.isAuthorized {
                        Button("Enable Notifications") {
                            Task {
                                await pushNotificationService.requestAuthorization()
                            }
                        }
                    }
                    
                    if useAsyncProcessing && !pushNotificationService.isAuthorized {
                        Text("Push notifications are required for asynchronous processing.")
                            .font(.caption)
                            .foregroundColor(.orange)
                    }
                }
                
                Section("Current Activity") {
                    HStack {
                        Text("Pending Requests")
                        Spacer()
                        Text("\(asyncProcessingService.pendingRequests.count)")
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Text("Ready Results")
                        Spacer()
                        Text("\(asyncProcessingService.readyRequestIds.count)")
                            .foregroundColor(asyncProcessingService.readyRequestIds.isEmpty ? .secondary : .green)
                    }
                    
                    if asyncProcessingService.hasDataReady {
                        Button("Fetch Ready Results") {
                            asyncProcessingService.fetchReadyResults()
                        }
                    }
                    
                    if !asyncProcessingService.pendingRequests.isEmpty {
                        Button("Clear All Requests", role: .destructive) {
                            asyncProcessingService.clearAllRequests()
                        }
                    }
                }
                
                Section("Debug") {
                    if let deviceToken = pushNotificationService.deviceToken {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Device Token")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Text("\(deviceToken.prefix(16))...")
                                .font(.system(.caption, design: .monospaced))
                        }
                    }
                    
                    Button("Simulate Notification") {
                        // Create a dummy request for testing
                        let testRequestId = "test-\(UUID().uuidString)"
                        asyncProcessingService.submitRequest(
                            requestId: testRequestId,
                            imageCount: 3
                        )
                        
                        // Simulate notification after 3 seconds
                        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                            asyncProcessingService.simulateNotificationReceived(requestId: testRequestId)
                        }
                    }
                }
            }
            .navigationTitle("Settings")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct SettingsView_Previews: PreviewProvider {
    static var previews: some View {
        SettingsView()
            .environmentObject(PushNotificationService.shared)
            .environmentObject(AsyncProcessingService.shared)
    }
}
