import SwiftUI

// MARK: - Round Button Style
enum RoundButtonStyle {
    case glassmorphic
    case solid
    case outlined
}

// MARK: - Round Button
struct RoundButton: View {
    let sfSymbol: String
    let action: () -> Void
    
    var isEnabled: Bool = true
    var isVisible: Bool = true
    var size: CGFloat = 60
    var iconSize: CGFloat = 24
    var style: RoundButtonStyle = .glassmorphic
    var foregroundColor: Color = .primary
    var backgroundColor: Color = .clear
    var animateEntrance: Bool = true
    var animateScale: Bool = false
    
    @State private var pulse: Bool = false
    @ScaledMetric private var scaledIconSize: CGFloat = 24
    
    private var effectiveIconSize: CGFloat {
        iconSize > 0 ? iconSize : scaledIconSize
    }
    
    private var buttonOpacity: Double {
        isEnabled ? 1.0 : 0.5
    }
    
    private var buttonForegroundColor: Color {
        isEnabled ? foregroundColor : foregroundColor.opacity(0.5)
    }
    
    var body: some View {
        ZStack {
            if isVisible {
                Button(action: {
                    if isEnabled {
                        action()
                    }
                }) {
                    ZStack(alignment: .center) {
                        // Background based on style
                        backgroundView
                            .frame(width: size, height: size)
                        
                        // Icon
                        Image(systemName: sfSymbol)
                            .font(.system(size: effectiveIconSize, weight: .medium))
                            .foregroundColor(buttonForegroundColor)
                    }
                }
                .buttonStyle(PlainButtonStyle())
                .frame(width: size, height: size)
                .disabled(!isEnabled)
                .opacity(buttonOpacity)
                .scaleEffect(animateScale && pulse ? 1.05 : 1.0)
                .transition(
                    animateEntrance ? 
                        .asymmetric(
                            insertion: AnyTransition.scale(scale: 0.3, anchor: .bottom).combined(with: .opacity),
                            removal: AnyTransition.scale(scale: 0.3, anchor: .bottom).combined(with: .opacity)
                        ) : .opacity
                )
                .onAppear { 
                    pulse = true
                    if animateScale {
                        withAnimation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true)) {
                            pulse.toggle()
                        }
                    }
                }
                .onDisappear { pulse = false }
            }
        }
        .frame(width: size, height: size)
        .animation(.spring(response: 0.35, dampingFraction: 0.78), value: isVisible)
        .animation(.easeInOut(duration: 0.3), value: isEnabled)
    }
    
    @ViewBuilder
    private var backgroundView: some View {
        switch style {
        case .glassmorphic:
            VisualEffectBlur(blurStyle: .systemUltraThinMaterial)
                .clipShape(Circle())
        case .solid:
            Circle()
                .fill(backgroundColor)
        case .outlined:
            Circle()
                .strokeBorder(foregroundColor, lineWidth: 2)
                .background(Circle().fill(backgroundColor))
        }
    }
}

// MARK: - Convenient Initializers
extension RoundButton {
    // Simple initializer with just symbol and action
    init(_ sfSymbol: String, action: @escaping () -> Void) {
        self.sfSymbol = sfSymbol
        self.action = action
    }
    
    // Builder-pattern style modifiers
    func enabled(_ isEnabled: Bool) -> RoundButton {
        var button = self
        button.isEnabled = isEnabled
        return button
    }
    
    func visible(_ isVisible: Bool) -> RoundButton {
        var button = self
        button.isVisible = isVisible
        return button
    }
    
    func size(_ size: CGFloat) -> RoundButton {
        var button = self
        button.size = size
        return button
    }
    
    func iconSize(_ iconSize: CGFloat) -> RoundButton {
        var button = self
        button.iconSize = iconSize
        return button
    }
    
    func style(_ style: RoundButtonStyle) -> RoundButton {
        var button = self
        button.style = style
        return button
    }
    
    func foregroundColor(_ color: Color) -> RoundButton {
        var button = self
        button.foregroundColor = color
        return button
    }
    
    func backgroundColor(_ color: Color) -> RoundButton {
        var button = self
        button.backgroundColor = color
        return button
    }
    
    func animateEntrance(_ animate: Bool = true) -> RoundButton {
        var button = self
        button.animateEntrance = animate
        return button
    }
    
    func animateScale(_ animate: Bool = true) -> RoundButton {
        var button = self
        button.animateScale = animate
        return button
    }
}

// MARK: - Preview
struct RoundButton_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            // Glassmorphic style (default)
            RoundButton("paperplane") {
                print("Send tapped")
            }
            
            // Solid style
            RoundButton("heart.fill") {
                print("Heart tapped")
            }
            .style(.solid)
            .backgroundColor(.red)
            .foregroundColor(.white)
            
            // Outlined style
            RoundButton("star") {
                print("Star tapped")
            }
            .style(.outlined)
            .foregroundColor(.blue)
            
            // Disabled state
            RoundButton("trash") {
                print("Delete tapped")
            }
            .enabled(false)
            
            // Custom size and animated
            RoundButton("bell") {
                print("Bell tapped")
            }
            .size(80)
            .iconSize(32)
            .animateScale(true)
            .foregroundColor(.orange)
        }
        .padding()
        .background(Color.gray.opacity(0.1))
    }
}
