import SwiftUI

struct SpinnerCircle: View {
    @Binding var isLoading: Bool
    @State private var isAnimating = false
    @State private var trimEnd: CGFloat = 0.22
    @State private var scale: CGFloat = 1.0
    var lineWidth: CGFloat = 4
    var size: CGFloat = 63
    var color: Color = .white

    var body: some View {
        Circle()
            .trim(from: 0.0, to: trimEnd)
            .stroke(style: StrokeStyle(lineWidth: lineWidth, lineCap: .round))
            .foregroundColor(color)
            .frame(width: size * scale, height: size * scale)
            .rotationEffect(.degrees(isAnimating ? 360 : 0))
            .onAppear {
                withAnimation(Animation.linear(duration: 0.8).repeatForever(autoreverses: false)) {
                    isAnimating = true
                }
            }
            .onChange(of: isLoading) { loading in
                if !loading {
                    // Animate to full circle and scale up
                    withAnimation(.easeOut(duration: 0.22)) {
                        trimEnd = 1.0
//                        scale = 1.12
                    }
                } else {
                    trimEnd = 0.22
                    scale = 1.0
                }
            }
    }
}
