import SwiftUI

/// Enhanced Capture Button with visual cues for hold-to-reveal functionality
struct CaptureButton: View {
	@Binding var isPressed: Bool
	let onTouchDown: () -> Void
	let onTouchUp: () -> Void
	let onTouchUpCancel: () -> Void
	let onLongPress: () -> Void

	// Configurable
	let size: CGFloat
	private let borderWidth: CGFloat = 4
	private let holdThreshold: TimeInterval = 0.1

	@State private var longPressTriggered = false
	@State private var pulseAnimation = false
	@State private var holdAnimation = false
	@State private var showTooltip = false
	@State private var hasBeenUsed = false
	@GestureState private var isDetectingPress = false

	var body: some View {
		ZStack {
			// Main circle border
			Circle()
				.strokeBorder(Color.white, lineWidth: borderWidth)
				.background(
					Circle()
						.fill(isPressed ? Color.white.opacity(0.2) : Color.clear)
				)

			// Pulsing inner ring (when not pressed)
			//			if !isPressed {
			//				Circle()
			//					.strokeBorder(Color.white.opacity(0.8), lineWidth: 2)
			//					.scaleEffect(pulseAnimation ? 0.7 : 1.0)
			//					.opacity(pulseAnimation ? 0 : 1)
			//					.animation(
			//						.easeInOut(duration: 3).repeatForever(autoreverses: false),
			//						value: pulseAnimation)
			//			}

			// Eye icon in center
			// Image(systemName: isPressed ? "eye.fill" : "eye")
			// 	.font(.system(size: size * 0.25, weight: .medium))
			// 	.foregroundColor(.white)
			// 	.scaleEffect(isPressed ? 1.2 : 1.0)
			// 	.opacity(isPressed ? 1.0 : 0.8)

			//			if #available(iOS 18.0, *) {
			//                Image(systemName: "hold.brakesignal")
			//                    .font(.system(size: size, weight: .medium))
			//                    .foregroundColor(.white)
			//                    .symbolEffect(.scale.down.wholeSymbol, options: .repeat(.periodic(2)))
			//			} else {
			Image(systemName: "hold.brakesignal")
				.scaleEffect(pulseAnimation ? 1.5 : 2.0)
				.foregroundColor(.white)
				.animation(
					.easeInOut(duration: 3).repeatForever(autoreverses: false),
					value: pulseAnimation
				)
			//			}

			// Glow effect when pressed
			if isPressed {
				Circle()
					.strokeBorder(Color.white.opacity(0.6), lineWidth: 8)
					.blur(radius: 4)
					.scaleEffect(1.05)
			}
		}
		.frame(width: size, height: size)
		.scaleEffect(isPressed ? 0.93 : 1.0)
		.animation(.spring(response: 0.23, dampingFraction: 0.7), value: isPressed)
		.accessibilityLabel("Hold to reveal camera, release to capture")
		.contentShape(Circle())
		.onAppear {
			pulseAnimation = true
			// Show tooltip after a brief delay
			DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
				if !hasBeenUsed {
					withAnimation(.easeInOut(duration: 0.3)) {
						showTooltip = true
					}
					// Hide tooltip after 3 seconds
					DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
						withAnimation(.easeInOut(duration: 0.3)) {
							showTooltip = false
						}
					}
				}
			}
		}
		.gesture(
			DragGesture(minimumDistance: 0)
				.onChanged { value in
					let location = value.location
					let radius = size / 2
					let center = CGPoint(x: radius, y: radius)
					let dx = location.x - center.x
					let dy = location.y - center.y
					let distance = sqrt(dx * dx + dy * dy)
					let inside = distance <= radius

					if inside {
						if !isPressed {
							let generator = UIImpactFeedbackGenerator(style: .rigid)
							generator.impactOccurred()
							isPressed = true
							hasBeenUsed = true  // Mark as used
							withAnimation(.easeOut(duration: 0.2)) {
								showTooltip = false
							}
							onTouchDown()
							startLongPressTimer()
						}
					} else {
						if isPressed {
							let generator = UIImpactFeedbackGenerator(style: .soft)
							generator.impactOccurred()
							isPressed = false
							onTouchUpCancel()
							cancelLongPressTimer()
						}
					}
				}
				.onEnded { value in
					let location = value.location
					let radius = size / 2
					let center = CGPoint(x: radius, y: radius)
					let dx = location.x - center.x
					let dy = location.y - center.y
					let distance = sqrt(dx * dx + dy * dy)
					let inside = distance <= radius

					if inside {
						let generator = UIImpactFeedbackGenerator(style: .soft)
						generator.impactOccurred()
						isPressed = false
						onTouchUp()
					}
					cancelLongPressTimer()
					longPressTriggered = false
				}
		)
	}

	// MARK: - Long Press Timer
	@State private var longPressTimer: Timer? = nil

	private func startLongPressTimer() {
		cancelLongPressTimer()
		longPressTimer = Timer.scheduledTimer(withTimeInterval: holdThreshold, repeats: false) {
			_ in
			if isPressed && !longPressTriggered {
				longPressTriggered = true
				onLongPress()
			}
		}
	}

	private func cancelLongPressTimer() {
		longPressTimer?.invalidate()
		longPressTimer = nil
	}

}

// MARK: - Hold Gesture Animation Component
struct HoldGestureAnimation: View {
	@State private var isAnimating = false
	@State private var fingerOpacity: Double = 0
	@State private var ringScale: CGFloat = 1.0
	@State private var ringOpacity: Double = 0

	let size: CGFloat

	var body: some View {
		ZStack {
			// Button representation
			Circle()
				.strokeBorder(Color.white.opacity(0.6), lineWidth: 3)
				.frame(width: size, height: size)
				.scaleEffect(isAnimating ? 0.93 : 1.0)

			// Eye icon
			Image(systemName: "eye")
				.font(.system(size: size * 0.25, weight: .medium))
				.foregroundColor(.white.opacity(0.8))

			// Animated press ring
			Circle()
				.strokeBorder(Color.white.opacity(ringOpacity), lineWidth: 6)
				.frame(width: size, height: size)
				.scaleEffect(ringScale)
				.blur(radius: 2)

			// Finger representation
			Circle()
				.fill(Color.white.opacity(fingerOpacity))
				.frame(width: size * 0.3, height: size * 0.3)
				.offset(x: size * 0.4, y: -size * 0.4)
		}
		.onAppear {
			startAnimation()
		}
	}

	private func startAnimation() {
		// Reset state
		isAnimating = false
		fingerOpacity = 0
		ringScale = 1.0
		ringOpacity = 0

		// Finger appears
		withAnimation(.easeInOut(duration: 0.5)) {
			fingerOpacity = 0.8
		}

		// Finger moves to button and presses
		DispatchQueue.main.asyncAfter(deadline: .now() + 0.8) {
			withAnimation(.easeInOut(duration: 0.3)) {
				fingerOpacity = 0.6
			}
			withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
				isAnimating = true
			}
			withAnimation(.easeInOut(duration: 0.2)) {
				ringOpacity = 0.8
				ringScale = 1.05
			}
		}

		// Hold state for 1 second
		DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
			withAnimation(.easeInOut(duration: 0.3)) {
				fingerOpacity = 0
				ringOpacity = 0
				ringScale = 1.0
			}
			withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
				isAnimating = false
			}
		}

		// Repeat animation
		DispatchQueue.main.asyncAfter(deadline: .now() + 3.5) {
			startAnimation()
		}
	}
}

// MARK: - Onboarding Overlay Component
struct CaptureButtonOnboarding: View {
	@Binding var isShowing: Bool
	let buttonSize: CGFloat
	let buttonPosition: CGPoint

	var body: some View {
		if isShowing {
			ZStack {
				// Semi-transparent overlay
				Color.black.opacity(0.7)
					.ignoresSafeArea()
					.onTapGesture {
						withAnimation(.easeInOut(duration: 0.3)) {
							isShowing = false
						}
					}

				VStack(spacing: 20) {
					Text("Hold to Reveal Camera")
						.font(.title2)
						.fontWeight(.semibold)
						.foregroundColor(.white)

					Text(
						"Press and hold the camera button to peek through the blur and see your shot"
					)
					.font(.body)
					.foregroundColor(.white.opacity(0.8))
					.multilineTextAlignment(.center)
					.padding(.horizontal, 40)

					// Animated demonstration
					HoldGestureAnimation(size: buttonSize * 1.2)
						.padding(.vertical, 20)

					Button("Got it") {
						withAnimation(.easeInOut(duration: 0.3)) {
							isShowing = false
						}
					}
					.font(.headline)
					.foregroundColor(.black)
					.padding(.horizontal, 24)
					.padding(.vertical, 12)
					.background(Color.white)
					.cornerRadius(25)
				}
			}
			.transition(.opacity)
		}
	}
}

// MARK: - Preview
struct CaptureButton_Previews: PreviewProvider {
	struct Wrapper: View {
		@State var pressed = false
		@State var showOnboarding = false

		var body: some View {
			ZStack {
				VStack(spacing: 50) {
					CaptureButton(
						isPressed: $pressed,
						onTouchDown: { print("Eye button - down") },
						onTouchUp: { print("Eye button - up") },
						onTouchUpCancel: { print("Eye button - cancel") },
						onLongPress: { print("Eye button - long press") },
						size: 80
					)

					Button("Show Onboarding") {
						showOnboarding = true
					}
					.foregroundColor(.white)
				}

				// Onboarding overlay
				CaptureButtonOnboarding(
					isShowing: $showOnboarding,
					buttonSize: 80,
					buttonPosition: CGPoint(x: 0, y: 0)
				)
			}
		}
	}

	static var previews: some View {
		Wrapper()
			.preferredColorScheme(.dark)
			.background(Color.black)
	}
}
