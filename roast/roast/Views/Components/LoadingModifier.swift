import SwiftUI

// MARK: - Loading State View Modifier
struct LoadingStateModifier: ViewModifier {
    @ObservedObject var loadingManager: LoadingStateManager
    
    func body(content: Content) -> some View {
        content
            .overlay(
                // Loading overlay
                Group {
                    if loadingManager.isLoading {
                        SimpleLoadingView(message: loadingManager.loadingMessage)
                            .zIndex(1000)
                    }
                }
            )
            .overlay(
                // Toast overlay
                Group {
                    if loadingManager.showToast {
                        ToastView(
                            toast: ToastMessage(
                                type: loadingManager.toastType,
                                title: loadingManager.toastMessage
                            ),
                            onDismiss: { loadingManager.showToast = false }
                        )
                        .zIndex(999)
                    }
                }
            )
    }
}

// MARK: - View Extension
extension View {
    func withLoadingState(_ loadingManager: LoadingStateManager) -> some View {
        self.modifier(LoadingStateModifier(loadingManager: loadingManager))
    }
}

// MARK: - Haptic Feedback Helper
struct HapticFeedback {
    static func impact(_ style: UIImpactFeedbackGenerator.FeedbackStyle = .medium) {
        let generator = UIImpactFeedbackGenerator(style: style)
        generator.impactOccurred()
    }
    
    static func notification(_ type: UINotificationFeedbackGenerator.FeedbackType) {
        let generator = UINotificationFeedbackGenerator()
        generator.notificationOccurred(type)
    }
    
    static func selection() {
        let generator = UISelectionFeedbackGenerator()
        generator.selectionChanged()
    }
}

// MARK: - Action Button with Loading State
struct ActionButton: View {
    let title: String
    let action: () -> Void
    let style: ButtonStyle
    @Binding var isLoading: Bool
    
    enum ButtonStyle {
        case primary, secondary, destructive
        
        var backgroundColor: Color {
            switch self {
            case .primary: return .accentColor
            case .secondary: return .secondary
            case .destructive: return .red
            }
        }
    }
    
    var body: some View {
        Button(action: {
            HapticFeedback.impact(.light)
            action()
        }) {
            HStack(spacing: 8) {
                if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.8)
                } else {
                    Text(title)
                        .fontWeight(.semibold)
                }
            }
            .foregroundColor(.white)
            .padding(.horizontal, 20)
            .padding(.vertical, 12)
            .background(style.backgroundColor.opacity(isLoading ? 0.7 : 1.0))
            .cornerRadius(25)
            .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        }
        .disabled(isLoading)
        .scaleEffect(isLoading ? 0.95 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: isLoading)
    }
}

// MARK: - Progressive Image Loader
struct ProgressiveImageView: View {
    let url: URL?
    let placeholder: String
    
    @State private var isLoading = true
    @State private var loadedImage: UIImage?
    
    var body: some View {
        ZStack {
            if let image = loadedImage {
                Image(uiImage: image)
                    .resizable()
                    .transition(.opacity.combined(with: .scale(scale: 1.05)))
            } else {
                // Skeleton placeholder
                SkeletonView()
                    .overlay(
                        Text(placeholder)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    )
            }
            
            if isLoading && loadedImage == nil {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: .secondary))
            }
        }
        .onAppear {
            loadImage()
        }
    }
    
    private func loadImage() {
        guard let url = url else {
            isLoading = false
            return
        }
        
        // Simulate network loading
        DispatchQueue.main.asyncAfter(deadline: .now() + Double.random(in: 1...3)) {
            withAnimation(.easeInOut(duration: 0.5)) {
                isLoading = false
                // In real implementation, load actual image here
            }
        }
    }
}

// MARK: - Preview
struct LoadingModifier_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            ActionButton(
                title: "Save Changes",
                action: {},
                style: .primary,
                isLoading: .constant(false)
            )
            
            ActionButton(
                title: "Processing...",
                action: {},
                style: .primary,
                isLoading: .constant(true)
            )
            
            ProgressiveImageView(
                url: URL(string: "https://example.com/image.jpg"),
                placeholder: "📷"
            )
            .frame(height: 200)
            .cornerRadius(12)
        }
        .padding()
    }
}
