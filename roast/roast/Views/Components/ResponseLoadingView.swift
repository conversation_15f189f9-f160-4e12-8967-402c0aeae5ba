import SwiftUI

// MARK: - Enhanced Response Loading View
struct ResponseLoadingView: View {
    @State private var animationProgress: CGFloat = 0
    @State private var messageIndex = 0
    @State private var showDetails = false

    private let loadingMessages = [
        "Observing your images...",
        "Analyzing your ingredients...",
        "Checking expiration dates...",
        "Categorizing...",
        "Final touches...",
        // "Finding perfect recipes...",
        // "Calculating cooking times...",
        "Almost ready!",
    ]

    var body: some View {
        Color.black.opacity(0.85)
            .ignoresSafeArea()
            .overlay(
                VStack(spacing: 32) {
                    // MARK: - Animated Chef Icon
                    ZStack {
                        // Pulsing background circle
                        Circle()
                            .fill(
                                RadialGradient(
                                    gradient: Gradient(colors: [
                                        Color.white.opacity(0.2),
                                        Color.white.opacity(0.05),
                                    ]),
                                    center: .center,
                                    startRadius: 20,
                                    endRadius: 80
                                )
                            )
                            .frame(width: 120, height: 120)
                            .scaleEffect(1.0 + animationProgress * 0.3)
                            .opacity(0.8 - animationProgress * 0.3)

                        // Chef emoji with cooking animation
                        Image("cookAgent")
                            .resizable()
                            .scaledToFit()
                            .frame(width: 80, height: 80)
                            .scaleEffect(1.0 + animationProgress * 0.1)
                            .rotationEffect(.degrees(animationProgress * 360))
                    }

                    // MARK: - Progress Indicator
                    VStack(spacing: 16) {
                        // Custom progress bar
                        ZStack(alignment: .leading) {
                            RoundedRectangle(cornerRadius: 8)
                                .fill(Color.white.opacity(0.2))
                                .frame(height: 8)
                                .frame(width: 200)

                            RoundedRectangle(cornerRadius: 8)
                                .fill(
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            Color.blue.opacity(0.8),
                                            Color.cyan.opacity(0.8),
                                        ]),
                                        startPoint: .leading,
                                        endPoint: .trailing
                                    )
                                )
                                .frame(height: 8)
                                .frame(width: 200 * animationProgress)
                                .animation(.easeInOut(duration: 3), value: animationProgress)
                        }

                        // Progress percentage
                        Text("\(Int(animationProgress * 100))%")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.8))
                            .fontWeight(.medium)
                    }

                    // MARK: - Dynamic Messages
                    VStack(spacing: 12) {
                        Text(loadingMessages[messageIndex])
                            .font(.title2)
                            .foregroundColor(.white)
                            .fontWeight(.semibold)
                            .multilineTextAlignment(.center)
                            .transition(
                                .asymmetric(
                                    insertion: .move(edge: .bottom).combined(with: .opacity),
                                    removal: .move(edge: .top).combined(with: .opacity)
                                ))

                        // Tips section
                        if showDetails {
                            VStack(spacing: 8) {
                                Text("💡 Tip: Take clear photos for better ingredient recognition")
                                    .font(.caption)
                                    .foregroundColor(.white.opacity(0.8))
                                    .multilineTextAlignment(.center)
                                    .transition(.opacity.combined(with: .move(edge: .bottom)))
                            }
                        }
                    }
                    .frame(height: 80)  // Fixed height to prevent layout shifts

                    Spacer()
                }
                .padding(.horizontal, 40)
                .padding(.top, 100)
            )
            .onAppear {
                startAnimations()
            }
    }

    private func startAnimations() {
        // Progress animation
        withAnimation(.easeInOut(duration: 60.0)) {
            animationProgress = 1.0
        }

        // Message cycling
        Timer.scheduledTimer(withTimeInterval: 8.0, repeats: false) { timer in
            withAnimation(.easeInOut(duration: 0.5)) {
                messageIndex = (messageIndex + 1) % loadingMessages.count
            }

            // Show tip after first message cycle
            if messageIndex == 3 && !showDetails {
                withAnimation(.easeInOut(duration: 0.5).delay(0.5)) {
                    showDetails = true
                }
            }
        }
    }
}

// MARK: - Simple Loading Overlay (for quick operations)
struct SimpleLoadingView: View {
    let message: String
    @State private var rotation: Double = 0

    var body: some View {
        ZStack {
            Color.black.opacity(0.6)
                .ignoresSafeArea()

            VStack(spacing: 20) {
                // Rotating cooking icon
                Text("🍳")
                    .font(.system(size: 40))
                    .rotationEffect(.degrees(rotation))
                    .onAppear {
                        withAnimation(.linear(duration: 2.0).repeatForever(autoreverses: false)) {
                            rotation = 360
                        }
                    }

                Text(message)
                    .font(.headline)
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }
            .padding(30)
            .background(.ultraThinMaterial)
            .cornerRadius(20)
        }
    }
}

// MARK: - Preview
struct LoadingViews_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            ResponseLoadingView()
                .preferredColorScheme(.dark)

            SimpleLoadingView(message: "Saving ingredient...")
                .preferredColorScheme(.light)

        }
    }
}
