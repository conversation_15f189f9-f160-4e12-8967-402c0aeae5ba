import SwiftUI

// Dummy overlay view for waiting state
struct ResponseLoadingView: View {
    var body: some View {
        Color.black.opacity(0.85)
            .ignoresSafeArea()
            .overlay(
                VStack(spacing: 20) {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(2.0)
                    Text("Waiting for server response...")
                        .font(.title2)
                        .foregroundColor(.white)
                        .bold()
                }
            )
            .animation(.easeInOut, value: UUID())
    }
}