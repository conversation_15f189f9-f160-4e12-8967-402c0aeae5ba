import SwiftUI

struct SkeletonView: View {
	@State private var animationOffset: CGFloat = -1

	var body: some View {
		GeometryReader { geometry in
			RoundedRectangle(cornerRadius: 8)
				.fill(
					LinearGradient(
						gradient: Gradient(colors: [
							Color.gray.opacity(0.3),
							Color.gray.opacity(0.1),
							Color.gray.opacity(0.3),
						]),
						startPoint: .init(x: animationOffset - 0.3, y: 0.5),
						endPoint: .init(x: animationOffset + 0.3, y: 0.5)
					)
				)
				.onAppear {
					withAnimation(
						Animation.linear(duration: 1.5)
							.repeatForever(autoreverses: false)
					) {
						animationOffset = 2
					}
				}
		}
	}
}