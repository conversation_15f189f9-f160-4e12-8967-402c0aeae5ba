import SwiftUI

// MARK: - Recipe Extensions
extension String {
    var difficultyIcon: String {
        switch self.lowercased() {
        case "easy": return "😊"
        case "medium", "moderate": return "😐"
        case "hard", "difficult": return "😅"
        default: return "🍽️"
        }
    }
    
    var difficultyColor: Color {
        switch self.lowercased() {
        case "easy": return .green
        case "medium", "moderate": return .orange
        case "hard", "difficult": return .red
        default: return .gray
        }
    }
}

struct RecipeCardView: View {
    let recipe: RecipeSeed
    let cardCornerRadius: CGFloat = 12
    @State private var isPressed = false

    var body: some View {
        ZStack {
            // Enhanced background with shadow
            RoundedRectangle(cornerRadius: cardCornerRadius, style: .continuous)
                .fill(.ultraThinMaterial)
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)

            VStack(alignment: .leading, spacing: 12) {
                // MARK: - Header with Recipe Name and Duration
                HStack(alignment: .top, spacing: 12) {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(recipe.name)
                            .font(.title3)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                            .multilineTextAlignment(.leading)
                        
//                        Text(recipe.title)
//                            .font(.subheadline)
//                            .foregroundColor(.secondary)
//                            .lineLimit(2)
                    }
                    
                    Spacer()
                    
                    // Duration badge
                    HStack(spacing: 4) {
                        Image(systemName: "clock")
                            .font(.caption)
                        Text(recipe.duration)
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.blue.opacity(0.15))
                    .foregroundColor(.blue)
                    .cornerRadius(6)
                }
                
                // MARK: - Difficulty and Flavor Profile
                HStack(spacing: 12) {
                    // Difficulty indicator
                    HStack(spacing: 4) {
                        Text(recipe.difficulty.difficultyIcon)
                            .font(.caption)
                        Text(recipe.difficulty.capitalized)
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(recipe.difficulty.difficultyColor.opacity(0.15))
                    .foregroundColor(recipe.difficulty.difficultyColor)
                    .cornerRadius(6)
                    
                    // Flavor profile
                    Text(recipe.flavor_profile)
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.purple.opacity(0.15))
                        .foregroundColor(.purple)
                        .cornerRadius(6)
                    
                    Spacer()
                }
                
                // MARK: - Tags
                if !recipe.key_tags.isEmpty {
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 6) {
                            ForEach(recipe.key_tags.prefix(4), id: \.self) { tag in
                                Text("#\(tag.lowercased())")
                                    .font(.caption2)
                                    .padding(.horizontal, 6)
                                    .padding(.vertical, 2)
                                    .background(Color.gray.opacity(0.15))
                                    .foregroundColor(.secondary)
                                    .cornerRadius(4)
                            }
                            
                            if recipe.key_tags.count > 4 {
                                Text("+\(recipe.key_tags.count - 4)")
                                    .font(.caption2)
                                    .padding(.horizontal, 6)
                                    .padding(.vertical, 2)
                                    .background(Color.gray.opacity(0.15))
                                    .foregroundColor(.secondary)
                                    .cornerRadius(4)
                            }
                        }
                    }
                }
                
                // MARK: - Description
                Text(recipe.description)
                    .font(.footnote)
                    .foregroundColor(.secondary)
                    .lineLimit(3)
                    .multilineTextAlignment(.leading)
            }
            .padding(16)
        }
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(.easeInOut(duration: 0.1), value: isPressed)
        .onLongPressGesture(
            minimumDuration: 0,
            maximumDistance: .infinity,
            pressing: { pressing in
                isPressed = pressing
            },
            perform: {}
        )
        .accessibilityElement(children: .combine)
        .accessibilityLabel("Recipe: \(recipe.name), \(recipe.difficulty) difficulty, \(recipe.duration)")
    }
}

#Preview {
    VStack(spacing: 16) {
        RecipeCardView(recipe: RecipeSeed.mock)
        
        RecipeCardView(recipe: RecipeSeed(
            id: UUID(),
            name: "Quick Pasta",
            description: "A fast and delicious pasta dish perfect for busy weeknights with fresh ingredients.",
            origin: "Italian",
            duration: "20 mins",
            difficulty: "easy",
            instructions: ["1. Hello", "2. Goodbye"],
            flavor_profile: "Italian",
            key_tags: ["pasta", "quick", "italian", "comfort", "family-friendly"],
            ingredients: ["thing1", "thing2"]
        ))
    }
    .padding()
}
