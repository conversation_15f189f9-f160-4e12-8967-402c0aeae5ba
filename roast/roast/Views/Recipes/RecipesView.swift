import SwiftUI

// will need to create a RecipeChatViewModel
// for interactivity with recipes (and ingredients) in context
struct RecipesView: View {
    @EnvironmentObject var appDataVM: AppDataViewModel
    @Binding var path: [AppFlowStep]

    @ObservedObject var viewModel: RecipesViewModel

    @State private var recipeToCreate: RecipeSeed? = nil
    @State private var recipeToModify: RecipeSeed? = nil
    @StateObject private var loadingManager = LoadingStateManager()

    init(path: Binding<[AppFlowStep]>, viewModel: RecipesViewModel) {
        _path = path
        self.viewModel = viewModel
    }

    private var recipesLazyVStack: some View {
        ScrollView {
            ForEach(appDataVM.recipeSeeds, id: \.id) { recipeSeed in
                let availableIngredients = appDataVM.inventory.map { $0.name }
                Menu {
                    Button(action: {
                        viewModel.createRecipeRequest(
                            for: recipeSeed,
                            availableIngredients: availableIngredients
                        ) { _ in }
                        // recipeToCreate = recipeSeed
                        path.append(.recipe(recipeSeed.id))
                    }) {
                        Text("Create")
                    }
                    Button(action: {
                        recipeToModify = recipeSeed
                        // viewModel.editBasicRecipe(recipe)
                    }) {
                        Text("Modify and Create")
                    }
                } label: {
                    RecipeCardView(recipe: recipeSeed)
                        .padding(.horizontal, 16)
                }
            }
        }
    }

    private var loadingView: some View {
        // Show skeleton loading for recipes
        VStack {
            HStack {
                Text("🍳")
                    .font(.system(size: 40))
                VStack(alignment: .leading) {
                    Text("Generating Recipes...")
                        .font(.title2)
                        .fontWeight(.semibold)
                    Text("Finding the perfect dishes for your ingredients")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
            }
            .padding(.horizontal)

            // Recipe card skeletons
            ForEach(0..<3, id: \.self) { _ in
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        SkeletonView()
                            .frame(height: 24)
                        Spacer()
                        SkeletonView()
                            .frame(width: 60, height: 20)
                    }
                    SkeletonView()
                        .frame(height: 16)
                    SkeletonView()
                        .frame(height: 40)
                }
                .padding(16)
                .background(.ultraThinMaterial)
                .cornerRadius(12)
                .padding(.horizontal, 16)
            }
            
            Spacer()
        }
    }

    var body: some View {
        ZStack {
            if appDataVM.recipeSeeds.isEmpty {
                loadingView
            } else {
                recipesLazyVStack
            }
        }
        .overlay(
            // Loading overlay
            Group {
                if loadingManager.isLoading {
                    SimpleLoadingView(message: loadingManager.loadingMessage)
                        .zIndex(2)
                }
            }
        )
        // .overlay(
        //     // Toast overlay
        //     Group {
        //         if loadingManager.showToast {
        //             ToastView(
        //                 message: loadingManager.toastMessage,
        //                 type: loadingManager.toastType,
        //                 isShowing: $loadingManager.showToast
        //             )
        //         }
        //     }
        // )
        .navigationTitle("Recipes")
        .navigationBarTitleDisplayMode(.large)
        .sheet(item: $recipeToModify) { recipeSeed in
            // EditRecipeView(recipe: recipeSeed)
        }
    }
}

// RecipesView Preview
struct RecipesView_Previews: PreviewProvider {
    static var previews: some View {
        @StateObject var mockVM = AppDataViewModel()
        let mockviewModel = RecipesViewModel(appDataVM: mockVM)

        RecipesView(path: .constant([]), viewModel: mockviewModel)
            .environmentObject(mockVM)
    }
}
