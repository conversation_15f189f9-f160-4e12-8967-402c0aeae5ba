import SwiftUI

struct RecipeCardView: View {
    let recipe: RecipeSeed
    let cardCornerRadius: CGFloat = 8

    var body: some View {
        ZStack(alignment: .topTrailing) {
            RoundedRectangle(cornerRadius: cardCornerRadius, style: .continuous)
                .fill(.ultraThinMaterial)

            VStack(alignment: .leading, spacing: 8) {
                // Top Row: Name, Duration
                HStack(alignment: .center) {
                    Text(recipe.name)
                        .font(.title3)
                        .fontWeight(.semibold)
                    Text(recipe.duration)
                        .font(.footnote)
                        .foregroundColor(.secondary)
                }
                Divider()
                // Row: Difficulty, flavor profile
                HStack {
                    Text(recipe.difficulty.capitalized)
                    Text(recipe.flavor_profile)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                Divider()
                // Row(s?): Key Tags
                HStack {
                    Text(recipe.key_tags.joined(separator: ", ").capitalized)
                        .font(.footnote)
                        .foregroundColor(.secondary)
                }
                Divider()
                // Row: Description
                HStack {
                    Text(recipe.description)
                        .font(.footnote)
                        .foregroundColor(.secondary)
                }
            }
        }
    }
}

#Preview {
    RecipeCardView(recipe: RecipeSeed.mock)
}
