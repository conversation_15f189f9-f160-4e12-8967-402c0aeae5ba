// import SwiftUI

// struct RecipeDetailView: View {
//     @EnvironmentObject var appDataVM: AppDataViewModel

//     let viewModel: RecipesViewModel
//     let recipeSeed: RecipeSeed

//     private var storedRecipe: StoredRecipe? {
//         appDataVM.storedRecipes.first { $0.id == recipeSeed.id }
//     }

//     var body: some View {
//         ScrollView {
//             VStack(alignment: .leading, spacing: 16) {
//                 if viewModel.isSending && storedRecipe?.recipe == nil {
//                     Text("Generating Recipe...")
//                         .foregroundColor(.secondary)
//                 } else if let generatedRecipe = storedRecipe?.recipe {
//                     Text(generatedRecipe.name)
//                         .font(.title2)
//                         .fontWeight(.bold)

//                     if let prepTime = generatedRecipe.prep_time {
//                         HStack {
//                             Text("Prep Time:")
//                                 .font(.footnote)
//                                 .foregroundColor(.secondary)
//                             Text(prepTime)
//                         }
//                     }

//                     HStack {
//                         Text("Cook Time:")
//                             .font(.footnote)
//                             .foregroundColor(.secondary)
//                         Text(generatedRecipe.cook_time)
//                     }

//                     HStack {
//                         Text("Servings:")
//                             .font(.footnote)
//                             .foregroundColor(.secondary)
//                         Text("\(generatedRecipe.servings)")
//                     }

//                     Divider()

//                     Text("Ingredients")
//                         .font(.headline)
//                     ForEach(generatedRecipe.ingredients, id: \.self) { ingredient in
//                         Text("• \(ingredient)")
//                             .padding(.leading, 8)
//                     }

//                     Divider()

//                     Text("Instructions")
//                         .font(.headline)
//                     ForEach(Array(generatedRecipe.instructions.enumerated()), id: \.offset) { idx, step in
//                         Text("\(idx + 1). \(step)")
//                             .padding(.leading, 8)
//                     }
//                 } else {
//                     Text("No recipe found for this seed.")
//                         .foregroundColor(.secondary)
//                 }
//             }
//             .padding()
//         }
//         .navigationTitle(recipeSeed.name)
//     }
// }

// struct RecipeDetailView_Previews: PreviewProvider {
//     static var previews: some View {
//         let mockRecipeSeed = RecipeSeed.mock
//         let mockRecipe = Recipe.mock
//         let mockStoredRecipe = StoredRecipe(
//             id: mockRecipeSeed.id, recipeSeed: mockRecipeSeed, recipe: mockRecipe)

//         let mockAppDataVM = AppDataViewModel()
//         mockAppDataVM.storedRecipes = [mockStoredRecipe]

//         let mockviewModel = RecipesViewModel(appDataVM: mockAppDataVM)

//         return RecipeDetailView(viewModel: mockviewModel, recipeSeed: mockRecipeSeed)
//             .environmentObject(mockAppDataVM)
//     }
// }


import SwiftUI

struct RecipeDetailView: View {
    @EnvironmentObject var appDataVM: AppDataViewModel

    let viewModel: RecipesViewModel
    let recipeSeed: RecipeSeed

    private var storedRecipe: StoredRecipe? {
        appDataVM.storedRecipes.first { $0.id == recipeSeed.id }
    }

    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 24) {
                if viewModel.isSending && storedRecipe?.recipe == nil {
                    Text("Generating Recipe...")
                        .foregroundColor(.secondary)
                        .padding()
                } else if let recipe = storedRecipe?.recipe {
                    // Recipe Name
                    Text(recipe.name)
                        .font(.title)
                        .bold()
                        .padding(.bottom, -8)

                    // Meta Info Section
                    HStack(spacing: 16) {
                        if let prepTime = recipe.prep_time {
                            Label(prepTime, systemImage: "clock")
                        }
                        Label(recipe.cook_time, systemImage: "flame")
                        Label("\(recipe.servings)", systemImage: "person.2.fill")
                    }
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                    Divider()

                    // Ingredients Section
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Ingredients")
                            .font(.headline)

                        ForEach(recipe.ingredients, id: \.self) { ingredient in
                            HStack(alignment: .top) {
                                Text("•")
                                Text(ingredient)
                            }
                        }
                    }

                    Divider()

                    // Instructions Section
                    VStack(alignment: .leading, spacing: 16) {
                        Text("Instructions")
                            .font(.headline)

                        ForEach(Array(recipe.instructions.enumerated()), id: \.offset) {
                            idx, step in
                            VStack(alignment: .leading, spacing: 8) {
                                HStack(alignment: .top) {
                                    Text("\(idx + 1).")
                                        .bold()
                                    Text(step)
                                }
                                .padding()
                                .background(Color(.secondarySystemBackground))
                                .cornerRadius(8)
                            }
                        }
                    }
                } else {
                    Text("No recipe found for this seed.")
                        .foregroundColor(.secondary)
                        .padding()
                }
            }
            .padding()
        }
        .navigationTitle(recipeSeed.name)
        .navigationBarTitleDisplayMode(.inline)
    }
}

//struct RecipeDetailView_Previews: PreviewProvider {
//    static var previews: some View {
//        let mockRecipeSeed = RecipeSeed.mock
//        let mockRecipe = Recipe.mock
//        let mockStoredRecipe = StoredRecipe(
//            id: mockRecipeSeed.id,
//            recipeSeed: mockRecipeSeed,
//            recipe: mockRecipe
//        )
//
//        let mockAppDataVM = AppDataViewModel()
//        mockAppDataVM.storedRecipes = [mockStoredRecipe]
//
//        let mockViewModel = RecipesViewModel(appDataVM: mockAppDataVM)
//
//        return RecipeDetailView(viewModel: mockViewModel, recipeSeed: mockRecipeSeed)
//            .environmentObject(mockAppDataVM)
//    }
//}
