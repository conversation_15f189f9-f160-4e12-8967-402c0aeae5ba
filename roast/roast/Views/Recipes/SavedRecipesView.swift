import SwiftUI

struct SavedRecipesView: View {
    @EnvironmentObject var appDataVM: AppDataViewModel
    @Binding var path: [AppFlowStep]
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                if appDataVM.storedRecipes.isEmpty {
                    VStack(spacing: 16) {
                        Image(systemName: "book.closed")
                            .font(.system(size: 60))
                            .foregroundColor(.secondary)
                        
                        Text("No Saved Recipes")
                            .font(.title2)
                            .fontWeight(.semibold)
                            .foregroundColor(.secondary)
                        
                        Text("Your saved recipes will appear here once you create and save them from the recipe suggestions.")
                            .font(.body)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)
                    }
                    .padding(.top, 100)
                } else {
                    ForEach(appDataVM.storedRecipes, id: \.id) { storedRecipe in
                        Button(action: {
                            path.append(.recipe(storedRecipe.recipeSeed.id))
                        }) {
                            SavedRecipeCardView(storedRecipe: storedRecipe)
                        }
                        .buttonStyle(PlainButtonStyle())
                        .padding(.horizontal)
                    }
                }
            }
        }
        .navigationTitle("Saved Recipes")
        .navigationBarTitleDisplayMode(.large)
    }
}

struct SavedRecipeCardView: View {
    let storedRecipe: StoredRecipe
    @State private var isPressed = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("🍽️")
                    .font(.title2)
                
                VStack(alignment: .leading, spacing: 4) {
            Text(storedRecipe.recipeSeed.name)
                .font(.headline)
                .fontWeight(.semibold)
//                .multilineTextAlignment(.leading)
//                    Text(storedRecipe.recipeSeed.title)
//                        .font(.subheadline)
//                        .foregroundColor(.secondary)
                }
                Spacer()
            }
            
            Text(storedRecipe.recipeSeed.description)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .lineLimit(2)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(16)
        .background(.ultraThinMaterial)
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 6, x: 0, y: 3)
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(.easeInOut(duration: 0.1), value: isPressed)
        .onLongPressGesture(
            minimumDuration: 0,
            maximumDistance: .infinity,
            pressing: { pressing in
                isPressed = pressing
            },
            perform: {}
        )
    }
}

struct SavedRecipesView_Previews: PreviewProvider {
    static var previews: some View {
        let mockVM = AppDataViewModel()
        SavedRecipesView(path: .constant([]))
            .environmentObject(mockVM)
    }
}
