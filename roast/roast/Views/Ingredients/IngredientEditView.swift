import SwiftUI

struct IngredientEditView: View {
    @Environment(\.dismiss) var dismiss
    @State var ingredient: Ingredient
    var onSave: (Ingredient) -> Void

    @StateObject private var loadingManager = LoadingStateManager()
    // MARK: - Expiration Date State
    @State private var showingDatePickerSheet: Bool = false
    @State private var expirationDate: Date? = nil
    @State private var datePickerSize: CGSize = .zero

    @FocusState private var isQuantityFocused: Bool
    
    // Formatter for date display
    private var dateFormatter: DateFormatter {
        let df = DateFormatter()
        df.dateFormat = "yyyy-MM-dd"
        return df
    }
    
    // On appear, sync expirationDate from ingredient.expiration_date
    private func syncExpirationDateFromString() {
        if let dateString = ingredient.expiration_date, let date = dateFormatter.date(from: dateString) {
            expirationDate = date
        } else {
            expirationDate = nil
        }
    }
    // On save, sync back to ingredient
    private func syncExpirationDateToString() {
        if let date = expirationDate {
            ingredient.expiration_date = dateFormatter.string(from: date)
        } else {
            ingredient.expiration_date = nil
        }
    }

    var body: some View {
        NavigationView {
            Form {
                Section(header: Text("General")) {
                    TextField("Name", text: $ingredient.name)
                    TextField("Brand", text: Binding(
                        get: { ingredient.brand ?? "" },
                        set: { ingredient.brand = $0.isEmpty ? nil : $0 }
                    ))
                    TextField("Category", text: $ingredient.food_category)
                    TextField("Diet (Organic, Vegan, etc)", text: Binding(
                        get: { ingredient.dietary_attributes ?? "" },
                        set: { ingredient.dietary_attributes = $0.isEmpty ? nil : $0 }
                    ))
                    TextField("Location", text: $ingredient.item_location)
                }
                Section(header: Text("Details")) {
                    TextField("Quantity", text: Binding(
                        get: { ingredient.quantity_value.clean },
                        set: { newValue in
                            if let floatVal = Float(newValue) {
                                ingredient.quantity_value = floatVal
                            } else if newValue.isEmpty {
                                ingredient.quantity_value = 0
                            }
                        }
                    ))
                        .keyboardType(.decimalPad)
                        .focused($isQuantityFocused)
                        .toolbar {
                            if isQuantityFocused {
                                ToolbarItem(placement: .keyboard) {
                                    Button("Done") {
                                        isQuantityFocused = false
                                    }
                                }
                            }
//                            ToolbarItemGroup(placement: .keyboard) {
//                                Spacer()
//                                Button("Done") {
//                                    isQuantityFocused = false
//                                }
//                            }
                        }
                    TextField("Unit", text: $ingredient.unit)
                    TextField("Container", text: $ingredient.container)
                    
                    // Expiration Date Row with Modal Sheet
                    Button(action: {
                        showingDatePickerSheet = true
                    }) {
                        HStack {
                            Text("Expiration Date")
                            Spacer()
                            if expirationDate != nil {
                                if #available(iOS 18.0, *) {
                                    Image(systemName: "minus.circle.fill")
                                        .foregroundColor(.primary)
                                        .symbolEffect(.bounce.down.byLayer, options: .nonRepeating)
                                        .onTapGesture {
                                            expirationDate = nil
                                        }
                                } else {
                                    // Fallback on earlier versions
                                    Image(systemName: "minus.circle.fill")
                                        .foregroundColor(.primary)
                                        .onTapGesture {
                                            expirationDate = nil
                                        }
                                }
                            }
                            ZStack(alignment: .center) {
                                if expirationDate == nil {
                                    Text("Set Date")
                                        .italic()
                                        .foregroundColor(.secondary)
                                        .frame(width: datePickerSize.width, height: datePickerSize.height)
                                        .background(
                                            RoundedRectangle(cornerRadius: 8)
                                                .fill(.ultraThinMaterial)
                                        )
                                        .allowsHitTesting(false)
                                }
                                DatePicker(
                                    "Expiration Date",
                                    selection: Binding(
                                        get: { expirationDate ?? Date() },
                                        set: { newValue in expirationDate = newValue }
                                    ),
                                    in: Date()..., // Prevent past dates
                                    displayedComponents: .date
                                )
                                .datePickerStyle(.compact)
                                .labelsHidden()
                                .opacity(expirationDate == nil ? 0.05 : 1.0)
                                .background(
                                    GeometryReader { geo in
                                        Color.clear
                                            .onAppear {
                                                datePickerSize = geo.size
                                            }
                                            .onChange(of: geo.size) { newSize in
                                                datePickerSize = newSize
                                            }
                                    }
                                )
                            }
                       }
                    }
                    // Toggle("Confident", isOn: $ingredient.is_confident)
                    // TextField("Note", text: Binding(
                    //     get: { ingredient.note ?? "" },
                    //     set: { ingredient.note = $0.isEmpty ? nil : $0 }
                    // ))
                }
            }
            .onAppear(perform: syncExpirationDateFromString)
            .navigationTitle("Edit Ingredient")
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Cancel") { dismiss() }
                }
                ToolbarItem(placement: .confirmationAction) {
                    Button("Save") {
                        print("[IngredientEditView] Saving ingredient: \(ingredient.name) - \(ingredient.id)")
                        loadingManager.showLoadingState("Saving ingredient...")
                        syncExpirationDateToString()
                        
                        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                            loadingManager.hideLoadingState()
                            onSave(ingredient)
                            dismiss()
                        }
                    }
                }
            }
        }
        .withLoadingState(loadingManager)
    }
}

// MARK: - Preview
struct IngredientEditView_Previews: PreviewProvider {
    static var previews: some View {
        IngredientEditView(
            ingredient: Ingredient(
                item_location: "top shelf",
                brand: "Heinz",
                name: "ketchup",
                food_category: "condiment",
                dietary_attributes: nil,
                quantity_value: 1,
                unit: "bottle",
                container: "bottle",
                expiration_date: "2025-06-01",
                is_confident: true,
                note: nil
            ),
            onSave: { _ in }
        )
    }
}
