import SwiftUI

struct IngredientCardSkeleton: View {
	var body: some View {
		VStack(alignment: .leading, spacing: 12) {
			HStack(spacing: 12) {
				// Icon skeleton
				SkeletonView()
					.frame(width: 30, height: 30)
					.clipShape(Circle())

				VStack(alignment: .leading, spacing: 4) {
					// Name skeleton
					SkeletonView()
						.frame(height: 20)
						.frame(maxWidth: .infinity)

					// Brand skeleton
					SkeletonView()
						.frame(height: 16)
						.frame(maxWidth: .infinity * 0.6)
				}

				Spacer()

				// Category badge skeleton
				SkeletonView()
					.frame(width: 60, height: 24)
					.cornerRadius(8)
			}

			// Details row skeleton
			HStack {
				SkeletonView()
					.frame(width: 80, height: 16)

				Spacer()

				SkeletonView()
					.frame(width: 60, height: 16)
			}

			// Bottom row skeleton
			HStack {
				SkeletonView()
					.frame(width: 100, height: 14)

				Spacer()

				SkeletonView()
					.frame(width: 80, height: 14)
			}
		}
		.padding(16)
		.background(.ultraThinMaterial)
		.cornerRadius(12)
	}
}