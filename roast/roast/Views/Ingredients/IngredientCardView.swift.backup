import SwiftUI

struct IngredientCardView: View {
    let ingredient: Ingredient
    let isSelected: Bool
    let cardCornerRadius: CGFloat = 8

    var body: some View {
        ZStack(alignment: .topTrailing) {
            RoundedRectangle(cornerRadius: cardCornerRadius, style: .continuous)
                .fill(.ultraThinMaterial)

            VStack(alignment: .leading, spacing: 8) {
                // Mark: - Top: Name, Attribute, Brand, Category
                HStack(alignment: .center) {
                    // Name and Attribute
                    VStack(alignment: .leading, spacing: 2) {
                        HStack {
                            // Name
                            Text(ingredient.name.capitalized)
                                .font(.title3)
                                .fontWeight(.semibold)
                            // Attribute
                            if let dietary = ingredient.dietary_attributes, !dietary.isEmpty {
                                Text(dietary.capitalized)
                                    .font(.footnote)
                                    .foregroundColor(.secondary)
                            }
                        }
                        // Brand
                        if let brand = ingredient.brand, !brand.isEmpty {
                            Text(brand.capitalized)
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                    }
                    Spacer()
                    // Category
                    Text(ingredient.food_category.capitalized)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }

                Divider()

                // Mark: - Bottom: Quantity, Unit, Expiration
                HStack(alignment: .center) {
                    // Quantity + Units
                    Text("\(ingredient.quantity_value.clean) \(ingredient.unit)")
                    Spacer()
                    if let expiration = ingredient.expiration_date, !expiration.isEmpty {
                        Text("EXP: \(expiration)")
                            .font(.caption)
                            .foregroundColor(.pink)
                    }
                    // VStack(alignment: .trailing, spacing: 2) {
                    //     Text(ingredient.is_confident ? "CONFIDENT" : "NOT CONFIDENT")
                    //         .font(.caption)
                    //         .foregroundColor(ingredient.is_confident ? .green : .red)
                    // }
                }
            }
            .padding()

        }
        .overlay(
            RoundedRectangle(cornerRadius: cardCornerRadius, style: .continuous)
                .strokeBorder(
                    isSelected ? Color(red: 0.53, green: 0.81, blue: 0.98) : Color.clear,
                    lineWidth: 4)
        )
        .clipShape(RoundedRectangle(cornerRadius: cardCornerRadius, style: .continuous))
        .contentShape(RoundedRectangle(cornerRadius: cardCornerRadius, style: .continuous))
        // .padding(.horizontal, 8)
    }
}

struct IngredientCardView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            IngredientCardView(
                ingredient: Ingredient(
                    item_location: "top shelf",
                    brand: "Heinz",
                    name: "ketchup",
                    food_category: "condiment",
                    dietary_attributes: nil,  // Missing dietary
                    quantity_value: 1,
                    unit: "bottle",
                    container: "bottle",
                    expiration_date: "2025-06-01",
                    is_confident: true,
                    note: nil
                ), isSelected: false)

            IngredientCardView(
                ingredient: Ingredient(
                    item_location: "fridge",
                    brand: nil,  // Missing brand
                    name: "milk",
                    food_category: "dairy",
                    dietary_attributes: "organic",
                    quantity_value: 2,
                    unit: "carton",
                    container: "carton",
                    expiration_date: "2025-04-30",
                    is_confident: true,
                    note: nil
                ), isSelected: false)

            IngredientCardView(
                ingredient: Ingredient(
                    item_location: "bottom shelf",
                    brand: "Land O' Lakes",
                    name: "butter",
                    food_category: "dairy",
                    dietary_attributes: "organic",
                    quantity_value: 2,
                    unit: "sticks",
                    container: "box",
                    expiration_date: "2025-04-30",
                    is_confident: false,
                    note: nil
                ), isSelected: false)
        }
    }
}
