import SwiftUI

struct IngredientsView: View {
    @EnvironmentObject var appDataVM: AppDataViewModel
    @Binding var path: [AppFlowStep]
    @ObservedObject private var viewModel: IngredientsViewModel
    @State private var detailIngredient: Ingredient? = nil
    // @State private var showDetailMenu: Bool = false
    // @State private var showEditSheet: Bool = false
    @State private var ingredientToEdit: Ingredient? = nil
    @StateObject private var loadingManager = LoadingStateManager()

    init(path: Binding<[AppFlowStep]>, viewModel: IngredientsViewModel) {
        self._path = path
        self.viewModel = viewModel
    }

    var ingredientLazyVStack: some View {
        ScrollView {
            LazyVStack(spacing: 8) {
                ForEach(appDataVM.inventory, id: \.id) { ingredient in
                    let isSelected = viewModel.selectedIngredients.contains(ingredient)
                    let isSelectedById = viewModel.selectedIngredients.contains {
                        $0.id == ingredient.id
                    }
                    let selectionLabel = isSelectedById ? "Deselect" : "Select"
                    let selectionIcon = isSelectedById ? "checkmark.circle.fill" : "circle"

                    IngredientCardView(
                        ingredient: ingredient,
                        isSelected: isSelected
                    )
                    .padding(.horizontal)
                    .onTapGesture {
                        viewModel.toggleSelection(for: ingredient)
                    }
                    .contextMenu {
                        Button {
                            viewModel.toggleSelection(for: ingredient)
                        } label: {
                            Label(
                                selectionLabel,
                                systemImage: selectionIcon
                            )
                        }
                        Button {
                            ingredientToEdit = ingredient
                            // showEditSheet = true
                        } label: {
                            Label("Edit", systemImage: "pencil")
                        }
                        Button(role: .destructive) {
                            viewModel.deleteIngredient(ingredient)
                        } label: {
                            Label("Delete", systemImage: "trash")
                        }
                    } preview: {
                        IngredientDetailView(
                            ingredient: ingredient,
                            isSelected: isSelected
                        )
                    }
                    // .opacity(detailIngredient?.id == ingredient.id ? 0 : 1)
                }
            }
        }
    }

    private var nextButton: some View {
        VStack {
            Spacer()
            HStack {
                Spacer()
                Button(action: {
                    path.append(.recipes)
                    viewModel.sendSelectedIngredients { passed in
                        guard passed else {
                            path.removeLast()
                            return
                        }
                    }
                }) {
                    Text("Next")
                        .font(.headline)
                        .foregroundColor(.white)
                        .padding(.vertical, 16)
                        .padding(.horizontal, 44)
                        .background(
                            viewModel.selectedIngredients.isEmpty
                                ? Color.gray : Color.accentColor
                        )
                        .cornerRadius(32)
                        .shadow(radius: 4)
                }
                .padding(.bottom, 32)
                .padding(.trailing, 24)
                .disabled(viewModel.selectedIngredients.isEmpty)
            }
        }
        .zIndex(1)
    }

    private var emptyView: some View {
        VStack(spacing: 24) {
            Image(systemName: "carrot")
                .font(.system(size: 60))
                .foregroundColor(.secondary)

            VStack(spacing: 8) {
                Text("No Ingredients Yet")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.secondary)

                Text(
                    "Take photos of your ingredients to get started with recipe suggestions."
                )
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            }

            Button("Take Photos") {
                path.removeAll()  // Go back to camera
            }
            .buttonStyle(.borderedProminent)
            .controlSize(.large)
        }
        .padding(.top, 80)
    }

    var body: some View {
        ZStack(alignment: .top) {
            if appDataVM.inventory.isEmpty {
                emptyView
            } else {
                ingredientLazyVStack
            }
            nextButton
        }
        .overlay(
            Group {
                if loadingManager.isLoading {
                    SimpleLoadingView(message: loadingManager.loadingMessage)
                        .zIndex(1000)
                }
            }
        )
        // .overlay(
        //     // Toast overlay
        //     Group {
        //         if loadingManager.showToast {
        //             ToastView(
        //                 message: loadingManager.toastMessage,
        //                 type: loadingManager.toastType,
        //                 isShowing: $loadingManager.showToast
        //             )
        //         }
        //     }
        // )
        .navigationTitle("Ingredients")
        .navigationBarTitleDisplayMode(.large)
        .tint(.primary)
        .sheet(item: $ingredientToEdit) { ingredient in
            IngredientEditView(ingredient: ingredient) { edited in
                viewModel.updateIngredient(edited)
                detailIngredient = edited
                ingredientToEdit = nil  // dismiss sheet after edit
            }
        }
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: {
                    loadingManager.showToast("Feature coming soon!", type: .info)
                }) {
                    Image(systemName: "plus")
                        .font(.system(size: 16, weight: .medium))
                }
                .accessibilityLabel("Add ingredient manually")
            }
        }
    }
}

// Dummy preview
// Note: IngredientCardView now expects isSelected argument
struct IngredientsView_Previews: PreviewProvider {
    static var previews: some View {
        let mockVM = AppDataViewModel()
        mockVM.inventory = [
            Ingredient(
                item_location: "top shelf", brand: "Heinz", name: "ketchup",
                food_category: "condiment", dietary_attributes: nil, quantity_value: 1,
                unit: "bottle", container: "bottle", expiration_date: "2025-06-01",
                is_confident: true, note: nil),
            Ingredient(
                item_location: "fridge", brand: nil, name: "milk", food_category: "dairy",
                dietary_attributes: "organic", quantity_value: 2, unit: "carton",
                container: "carton", expiration_date: "2025-04-30", is_confident: true,
                note: "Uncertain: possible 1L"),
            Ingredient(
                item_location: "bottom shelf", brand: "Land O'Lakes", name: "butter",
                food_category: "dairy", dietary_attributes: "organic", quantity_value: 2,
                unit: "sticks", container: "box", expiration_date: "2025-04-30",
                is_confident: false, note: "Uncertain: possible 1L"),
        ]

        let ingredientsVM = IngredientsViewModel(appDataVM: AppDataViewModel(), )

        return IngredientsView(path: .constant([]), viewModel: ingredientsVM)
    }
}
