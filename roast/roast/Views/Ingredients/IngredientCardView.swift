import SwiftUI

// MARK: - Category Icons and Colors
extension String {
    var categoryIcon: String {
        let category = self.lowercased()
        switch category {
        case "dairy": return "🥛"
        case "protein", "meat", "fish", "chicken", "beef": return "🍖"
        case "vegetable", "vegetables": return "🥕"
        case "fruit", "fruits": return "🍎"
        case "grain", "grains", "bread", "pasta": return "🌾"
        case "condiment", "condiments", "sauce", "spice", "spices": return "🧂"
        case "beverage", "drink": return "🥤"
        case "snack", "snacks": return "🍿"
        case "frozen": return "❄️"
        case "canned": return "🥫"
        default: return "🍽️"
        }
    }

    var categoryColor: Color {
        let category = self.lowercased()
        switch category {
        case "dairy": return .blue
        case "protein", "meat", "fish", "chicken", "beef": return .red
        case "vegetable", "vegetables", "legumes": return .orange
        case "fruit", "fruits": return .pink
        case "grain", "grains", "bread", "pasta": return .yellow
        case "condiment", "condiments", "sauce", "spice", "spices", "seasoning": return .purple
        case "beverage", "drink": return .cyan
        case "frozen": return .mint
        case "canned": return .brown
        default: return .gray
        }
    }
}

// MARK: - Enhanced Ingredient Card
struct IngredientCardView: View {
    let ingredient: Ingredient
    let isSelected: Bool
    let cardCornerRadius: CGFloat = 12

    @State private var isPressed = false

    private var isExpiringSoon: Bool {
        guard let expirationString = ingredient.expiration_date,
            !expirationString.isEmpty
        else { return false }

        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        guard let expirationDate = formatter.date(from: expirationString) else { return false }

        let daysUntilExpiration =
            Calendar.current.dateComponents([.day], from: Date(), to: expirationDate).day ?? 0
        return daysUntilExpiration <= 7 && daysUntilExpiration >= 0
    }

    private var isExpired: Bool {
        guard let expirationString = ingredient.expiration_date,
            !expirationString.isEmpty
        else { return false }

        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        guard let expirationDate = formatter.date(from: expirationString) else { return false }

        return expirationDate < Date()
    }

    var body: some View {
        ZStack {
            // Enhanced background with shadow
            RoundedRectangle(cornerRadius: cardCornerRadius, style: .continuous)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: cardCornerRadius, style: .continuous)
                        .strokeBorder(
                            // isSelected ? Color.accentColor : Color.clear,
                            isSelected ? Color(red: 0.53, green: 0.81, blue: 0.98) : Color.clear,
                            lineWidth: 4
                        )
                )

            VStack(alignment: .leading, spacing: 8) {
                // MARK: - Header with Icon and Category
                HStack(alignment: .center, spacing: 8) {
                    // Category Icon
                    // Text(ingredient.food_category.categoryIcon)
                    //     .font(.title2)

                    VStack(alignment: .leading, spacing: 4) {
                        // Name with brand
                        HStack(alignment: .firstTextBaseline, spacing: 6) {
                            Text(ingredient.name.capitalized)
                                .font(.title3)
                                .fontWeight(.semibold)
                            // .foregroundColor(.primary)

                            // Dietary attributes
                            if let dietary = ingredient.dietary_attributes, !dietary.isEmpty {
                                Text(dietary.capitalized)
                                    .font(.footnote)
                                // .padding(.horizontal, 8)
                                // .padding(.vertical, 2)
                                // .background(ingredient.food_category.categoryColor.opacity(0.2))
                                // .foregroundColor(ingredient.food_category.categoryColor)
                                // .cornerRadius(4)
                            }
                        }

                        if let brand = ingredient.brand, !brand.isEmpty {
                            Text(brand)
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                    }

                    Spacer()

                    // Category Badge
                    Text(ingredient.food_category.capitalized)
                        .font(.caption)
                        .fontWeight(.medium)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(ingredient.food_category.categoryColor.opacity(0.10))
                        .foregroundColor(ingredient.food_category.categoryColor)
                        .cornerRadius(8)
                }

                Divider()

                // MARK: - Details Row
                HStack(alignment: .center) {
                    // Quantity and Unit
                    HStack(spacing: 2) {
                        Image(systemName: "number")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text("\(ingredient.quantity_value.clean) \(ingredient.unit)")
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }

                    Spacer()

                    // Container info
                    HStack(spacing: 2) {
                        Image(systemName: "shippingbox")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text(ingredient.container.capitalized)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }

                // MARK: - Bottom Row with Location and Expiration
                HStack(alignment: .center) {
                    // Location
                    HStack(spacing: 2) {
                        Image(systemName: "location")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text(ingredient.item_location.capitalized)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    Spacer()

                    // Expiration with warning colors
                    if let expiration = ingredient.expiration_date, !expiration.isEmpty {
                        HStack(spacing: 2) {
                            Image(
                                systemName: isExpired
                                    ? "exclamationmark.triangle.fill"
                                    : isExpiringSoon ? "clock.fill" : "calendar"
                            )
                            .font(.caption)
                            .foregroundColor(
                                isExpired ? .red : isExpiringSoon ? .orange : .secondary)

                            Text(formatDate(expiration))
                                .font(.caption)
                                .fontWeight(isExpired || isExpiringSoon ? .semibold : .regular)
                                .foregroundColor(
                                    isExpired ? .red : isExpiringSoon ? .orange : .secondary)
                        }
                        .padding(.horizontal, isExpired || isExpiringSoon ? 6 : 0)
                        .padding(.vertical, 2)
                        .background(
                            (isExpired ? Color.red : isExpiringSoon ? Color.orange : Color.clear)
                                .opacity(0.1)
                        )
                        .cornerRadius(4)
                    }
                }
            }
            .padding(16)
        }
        .clipShape(RoundedRectangle(cornerRadius: cardCornerRadius, style: .continuous))
        .contentShape(RoundedRectangle(cornerRadius: cardCornerRadius, style: .continuous))
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(.easeInOut(duration: 0.1), value: isPressed)
        // .onLongPressGesture(
        //     minimumDuration: 0,
        //     maximumDistance: .infinity,
        //     pressing: { pressing in
        //         isPressed = pressing
        //     },
        //     perform: {}
        // )
        .accessibilityElement(children: .combine)
        .accessibilityLabel(
            "\(ingredient.name) \(ingredient.brand ?? ""), \(ingredient.quantity_value.clean) \(ingredient.unit)"
        )
        .accessibilityHint(isSelected ? "Selected" : "Tap to select")
    }

    private func formatDate(_ dateString: String) -> String {
        let inputFormatter = DateFormatter()
        inputFormatter.dateFormat = "yyyy-MM-dd"

        let outputFormatter = DateFormatter()
        outputFormatter.dateFormat = "MMM d"

        guard let date = inputFormatter.date(from: dateString) else {
            return dateString
        }

        return outputFormatter.string(from: date)
    }
}

struct IngredientCardView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 16) {
            IngredientCardView(
                ingredient: Ingredient(
                    item_location: "top shelf",
                    brand: "Heinz",
                    name: "ketchup",
                    food_category: "condiment",
                    dietary_attributes: "organic",
                    quantity_value: 1,
                    unit: "bottle",
                    container: "bottle",
                    expiration_date: "2025-06-01",
                    is_confident: true,
                    note: nil
                ), isSelected: false)

            IngredientCardView(
                ingredient: Ingredient(
                    item_location: "fridge",
                    brand: nil,
                    name: "milk",
                    food_category: "dairy",
                    dietary_attributes: "organic",
                    quantity_value: 2,
                    unit: "carton",
                    container: "carton",
                    expiration_date: "2024-12-30",  // Expired
                    is_confident: true,
                    note: nil
                ), isSelected: true)

            IngredientCardView(
                ingredient: Ingredient(
                    item_location: "pantry",
                    brand: "Dole",
                    name: "banana",
                    food_category: "fruit",
                    dietary_attributes: nil,
                    quantity_value: 6,
                    unit: "pieces",
                    container: "bunch",
                    expiration_date: "2024-12-31",  // Expiring soon
                    is_confident: true,
                    note: nil
                ), isSelected: false)
        }
        .padding()
        .background(Color(UIColor.systemBackground))
    }
}
