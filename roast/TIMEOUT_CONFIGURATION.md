# Timeout Configuration Guide

## Current Issue

You're experiencing 504 Gateway Timeout errors when processing multiple images. This happens because:

1. **nginx** (your reverse proxy) has a default timeout of ~60 seconds
2. **Your backend** is processing images which can take longer than 60 seconds for multiple/large images
3. **The iOS app** has a longer timeout (now dynamic: 180s + 30s per additional image) but nginx times out first

## Frontend Changes Made

### APIService Improvements
- Added specific **504 Gateway Timeout** error handling
- Shows user-friendly toast notifications for timeouts
- Better error logging with request size information
- Handles both client-side timeouts (NSURLErrorTimedOut) and server 504 responses

### CameraViewModel Improvements
- **Dynamic timeout calculation**: 180s base + 30s per additional image
- Better error handling with specific toast messages
- Success notifications when processing completes
- Improved user feedback for all error scenarios

### Toast Notification System
- App-wide toast notifications for all server responses
- Specific messages for 429 (rate limiting) and 504 (gateway timeout)
- Auto-dismissing with appropriate durations

## Server Configuration Needed

To fix the 504 errors, update your nginx configuration:

```nginx
server {
    # ... existing config ...
    
    # Increase proxy timeouts for image processing endpoint
    location /api/ingredients {
        proxy_pass http://your_upstream;
        proxy_read_timeout 300s;        # 5 minutes
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        
        # Optional: increase client body size for large images
        client_max_body_size 50M;
    }
    
    # Keep shorter timeouts for other endpoints
    location / {
        proxy_pass http://your_upstream;
        proxy_read_timeout 60s;         # 1 minute for other endpoints
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
    }
}
```

## Backend Considerations

1. **Add progress indicators**: Consider implementing streaming responses or progress callbacks
2. **Image optimization**: Resize/compress images before processing to reduce processing time
3. **Async processing**: Consider moving to async job processing for large batches
4. **Caching**: Cache processed results to avoid reprocessing identical images

## Testing

After updating nginx config:

1. **Test with single image**: Should complete quickly
2. **Test with 2-3 images**: Should complete within 3-4 minutes
3. **Test with many images**: App will show appropriate timeout messages if needed

## Monitoring

The app now logs:
- Request body sizes
- Timeout durations
- Response times
- Specific error types

Check your logs for patterns:
```bash
# iOS app logs
grep "\[APIService\]" your_device_logs

# nginx logs
tail -f /var/log/nginx/access.log | grep "api/ingredients"

# Backend logs
tail -f your_app_logs | grep "ingredients"
```
