import SwiftUI

struct DebugNotificationView: View {
    @StateObject private var asyncService = AsyncProcessingService.shared
    @StateObject private var pushService = PushNotificationService.shared
    
    var body: some View {
        NavigationView {
            List {
                Section("Push Notification Status") {
                    HStack {
                        Text("Authorized")
                        Spacer()
                        Image(systemName: pushService.isAuthorized ? "checkmark.circle.fill" : "xmark.circle.fill")
                            .foregroundColor(pushService.isAuthorized ? .green : .red)
                    }
                    
                    HStack {
                        Text("Device Token")
                        Spacer()
                        if let token = pushService.deviceToken {
                            Text(String(token.prefix(8)) + "...")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        } else {
                            Text("Not set")
                                .foregroundColor(.red)
                        }
                    }
                    
                    HStack {
                        Text("Ready Requests")
                        Spacer()
                        Text("\(asyncService.readyRequestIds.count)")
                            .foregroundColor(.blue)
                    }
                }
                
                Section("Actions") {
                    Button("Request Permission") {
                        Task {
                            await pushService.requestAuthorization()
                        }
                    }
                    
                    Button("Simulate Test Request") {
                        let testId = "test_\(Int.random(in: 1000...9999))"
                        asyncService.submitRequest(requestId: testId, imageCount: 2)
                        
                        // Simulate notification after 3 seconds
                        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                            asyncService.simulateNotificationReceived(requestId: testId)
                        }
                    }
                    
                    Button("Fetch Ready Results") {
                        asyncService.fetchReadyResults()
                    }
                    
                    Button("Clear All") {
                        asyncService.clearAllRequests()
                    }
                }
                
                if !asyncService.pendingRequests.isEmpty {
                    Section("Pending Requests") {
                        ForEach(asyncService.pendingRequests) { request in
                            VStack(alignment: .leading) {
                                Text(request.requestId)
                                    .font(.caption)
                                Text("Status: \(request.status.rawValue)")
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                }
            }
            .navigationTitle("Push Debug")
        }
    }
}

#Preview {
    DebugNotificationView()
}
