# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out

# Output of the go coverage tool, specifically when used with LiteIDE
*.cov

# Dependency directories (remove the comment below if you use vendoring)
# vendor/

# Go workspace file
*.code-workspace

# IDE/editor directories and files
.idea/
.vscode/
*.swp

# OS generated files
.DS_Store
Thumbs.db

# Local env files
.env
.env.*

# Go module files (lock files can be ignored if you prefer)
/go.sum
