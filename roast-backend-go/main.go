package main

import (
	"log"
	"net/http"

	"github.com/go-chi/chi/v5"

	"roast-backend-go/handlers"
	"roast-backend-go/middleware"
)

func main() {
	r := chi.NewRouter()

	r.Use(middleware.Logger)

	r.Route("/api", func(api chi.Router) {
		api.Get("/app", handlers.HQHandler)
		api.Post("/process", handlers.ProcessHandler)
		api.Post("/app/ios", handlers.IOSImagesHandler)
		// Existing routes:
		api.Post("/submit", handlers.SubmitHandler)
		api.Post("/register-device", handlers.RegisterDeviceHandler)
		api.Post("/login", handlers.LoginHandler)
		api.Post("/signup", handlers.SignupHandler)
		api.With(middleware.AuthMiddleware).Get("/protected", handlers.ProtectedHandler)
	})

	log.Println("[roast-backend-go] Server started on :8080")
	http.ListenAndServe(":8080", r)
}
