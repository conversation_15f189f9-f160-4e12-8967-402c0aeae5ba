# roast-backend-go

A Go-based backend service for the Marshmellow app, mirroring the functionality of the Python `roast-backend`.

## Features
- Receives user data and relays to 3rd party APIs
- Device token registration for push notifications
- Authentication endpoints (JWT-based, ready for OAuth)
- Rate-limiting for anonymous and authenticated users

## Getting Started
1. Install Go 1.20+
2. Run `go mod tidy`
3. Start the server: `go run main.go`

## Project Structure
- `main.go` - Entry point
- `handlers/` - HTTP handlers (API endpoints)
- `middleware/` - Authentication and rate limiting
- `models/` - Data models
- `config/` - Configuration loading

## TODO
- Implement APNs push notification logic
- Integrate with real 3rd party APIs
- Add persistent storage
