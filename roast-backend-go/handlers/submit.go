package handlers

import (
	"encoding/json"
	"net/http"
)

// SubmitHandler receives user data and relays to 3rd party APIs (stub).
func SubmitHandler(w http.ResponseWriter, r *http.Request) {
	var payload map[string]interface{}
	if err := json.NewDecoder(r.Body).Decode(&payload); err != nil {
		http.Error(w, "Invalid payload", http.StatusBadRequest)
		return
	}
	// TODO: Relay to 3rd party APIs
	w.WriteHeader(http.StatusOK)
	w.Write([]byte(`{"status":"received"}`))
}
