package handlers

import (
	"encoding/json"
	"net/http"
	"os"
)

type ProcessRequest struct {
	ImagePath string `json:"image_path"`
}

type ProcessResponse struct {
	Status string `json:"status"`
	// Add more fields as needed to match LLM response
}

// ProcessHandler handles image processing requests (form-based, stub).
func ProcessHandler(w http.ResponseWriter, r *http.Request) {
	imagePath := r.FormValue("image_path")
	if imagePath == "" {
		http.Error(w, "Missing image_path", http.StatusBadRequest)
		return
	}
	imagePathExpanded := os.ExpandEnv(imagePath)
	if _, err := os.Stat(imagePathExpanded); err != nil {
		http.Error(w, "Image file not found", http.StatusBadRequest)
		return
	}
	// TODO: Encode image, call LLM API, return structured response
	resp := ProcessResponse{Status: "received"}
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(resp)
}
