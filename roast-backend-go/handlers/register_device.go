package handlers

import (
	"encoding/json"
	"net/http"
)

type DeviceTokenRequest struct {
	DeviceToken string `json:"device_token"`
}

// RegisterDeviceHandler stores device tokens for push notifications (stub).
func RegisterDeviceHandler(w http.ResponseWriter, r *http.Request) {
	var req DeviceTokenRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil || req.DeviceToken == "" {
		http.Error(w, "Invalid device token", http.StatusBadRequest)
		return
	}
	// TODO: Store device token
	w.WriteHeader(http.StatusOK)
	w.Write([]byte(`{"status":"device registered"}`))
}
