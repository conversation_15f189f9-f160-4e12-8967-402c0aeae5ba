package handlers

import (
	"encoding/json"
	"log"
	"net/http"
	"os"

	openai "github.com/sasha<PERSON>nov/go-openai"
	"github.com/sashabaranov/go-openai/jsonschema"
)

type IOSRequest struct {
	Images  []string `json:"images"`
	Message string   `json:"message,omitempty"`
}

type IOSResponse struct {
	Status string `json:"status"`
	// Add more fields as needed to match LLM response
}

// IOSImagesHandler handles iOS image processing requests and calls the LLM API.
func IOSImagesHandler(w http.ResponseWriter, r *http.Request) {
	var req IOSRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}
	if len(req.Images) == 0 {
		http.Error(w, "No images provided", http.StatusBadRequest)
		return
	}

	apiKey := os.Getenv("OPENROUTER_API_KEY")
	if apiKey == "" {
		http.Error(w, "OPENROUTER_API_KEY not set in environment", http.StatusInternalServerError)
		return
	}

	client := openai.NewClient(apiKey)

	systemPrompt := `You are an AI assistant tasked with analyzing images of groceries to generate a detailed grocery list for recipe recommendations. Identify all grocery items, brand, name, category, dietary attributes, and quantity.`

	// Example JSON schema (expand as needed)
	ingredientSchema := jsonschema.Definition{
		Type: "object",
		Properties: map[string]jsonschema.Definition{
			"ingredients_list": {
				Type: "array",
				Items: &jsonschema.Definition{
					Type: "object",
					Properties: map[string]jsonschema.Definition{
						"item_location":      {Type: "string"},
						"brand":              {Type: "string"},
						"name":               {Type: "string"},
						"food_category":      {Type: "string"},
						"dietary_attributes": {Type: "string"},
						"quantity_value":     {Type: "string"},
					},
				},
			},
		},
	}

	schema, err := jsonschema.GenerateSchemaForType(ingredientSchema)
	if err != nil {
		log.Fatalf("GenerateSchemaForType failed: %v", err)
	}

	// Compose user content from images and message
	userContent := ""
	if req.Message != "" {
		userContent += req.Message + "\n"
	}
	userContent += "Images provided: " + string(len(req.Images))

	// Compose messages for ChatCompletion
	messages := []openai.ChatCompletionMessage{
		{Role: openai.ChatMessageRoleSystem, Content: systemPrompt},
		{Role: openai.ChatMessageRoleUser, Content: userContent},
	}

	// Call OpenAI-compatible API (OpenRouter)
	resp, err := client.CreateChatCompletion(r.Context(), openai.ChatCompletionRequest{
		Model:    "google/gemini-2.5-pro-preview-03-25",
		Messages: messages,
		ResponseFormat: &openai.ChatCompletionResponseFormat{
			Type: openai.ChatCompletionResponseFormatTypeJSONObject,
			JSONSchema: &openai.ChatCompletionResponseFormatJSONSchema{
				Name:   "ingredients_list",
				Schema: schema,
				Strict: true,
			},
		},
	})
	if err != nil {
		log.Printf("LLM API error: %v", err)
		http.Error(w, "Failed to call LLM API", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.Write([]byte(resp.Choices[0].Message.Content))
}
