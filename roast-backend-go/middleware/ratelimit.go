package middleware

import (
	"net/http"
	"time"
)

// TODO: Replace with Redis or persistent store for production.
var anonLastRequest = make(map[string]time.Time)

// RateLimitMiddleware limits requests for anonymous users (simple in-memory version).
func RateLimitMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		deviceID := r.Header.Get("X-Device-Id")
		if deviceID == "" {
			// No device ID, treat as anonymous
			deviceID = r.RemoteAddr
		}
		last, ok := anonLastRequest[deviceID]
		if ok && time.Since(last) < 10*time.Second {
			http.Error(w, "Rate limit exceeded. Please sign up for more access.", http.StatusTooManyRequests)
			return
		}
		anonLastRequest[deviceID] = time.Now()
		next.ServeHTTP(w, r)
	})
}
