import SwiftUI

struct CustomNavigationView: View {
    // MARK: - Persistent Storage
    @AppStorage("hasLaunchedBefore") private var hasLaunchedBefore: Bool = false
    @SceneStorage("currentScreenIndex") private var currentIndex = 1
    @State private var offset: CGFloat = 0

    // MARK: - View Model
    @StateObject private var viewModel = CustomNavigationViewModel()
    @StateObject private var cameraOverlayViewModel = CameraOverlayViewModel()

    // MARK: - Screen Enum
    enum Screen: Equatable {
        case cameraOverlay
        case ingredientsView
        case recipesView
    }

    // Screens always includes CameraOverlayView, others are conditional
    private var screens: [Screen] {
        var result: [Screen] = []
        if viewModel.shouldShowIngredientsView { result.append(.ingredientsView) }
        result.append(.cameraOverlay)
        if viewModel.shouldShowRecipesView { result.append(.recipesView) }
        return result
    }
    
    var body: some View {
        GeometryReader { geometry in
            let spacing: CGFloat = 12
            ZStack {
                // Global background color
                // Color(white: 0.21).ignoresSafeArea()
                // Color(hue: 0.63, saturation: 0.93, brightness: 0.11).ignoresSafeArea()

                HStack(spacing: spacing) {
                    ForEach(0..<screens.count, id: \ .self) { index in
                        screenView(for: screens[index])
                            .frame(width: geometry.size.width, height: geometry.size.height)
                            .ignoresSafeArea()
                    }
                }
                .offset(x: offset - CGFloat(currentIndex) * spacing)
                .gesture(
                    DragGesture()
                        .onChanged { value in
                            let minOffset = -CGFloat(screens.count - 1) * geometry.size.width
                            let maxOffset = CGFloat(0)
                            let proposedOffset = value.translation.width - CGFloat(currentIndex) * geometry.size.width
                            offset = min(max(proposedOffset, minOffset), maxOffset)
                        }
                        .onEnded { value in
                            let threshold = geometry.size.width / 2.5
                            let predictedEnd = value.predictedEndTranslation.width

                            if predictedEnd < -threshold && currentIndex < screens.count - 1 {
                                currentIndex += 1
                            } else if predictedEnd > threshold && currentIndex > 0 {
                                currentIndex -= 1
                            }
                            withAnimation(.snappy(duration: 0.2)) {
                                offset = -CGFloat(currentIndex) * geometry.size.width
                            }
                        }
                )
                .onAppear {
                    if currentIndex >= screens.count {
                        currentIndex = screens.firstIndex(of: .cameraOverlay) ?? 0
                    }
                    
                    if !hasLaunchedBefore {
                        if let cameraIndex = screens.firstIndex(of: .cameraOverlay) {
                            currentIndex = cameraIndex
                        }
                    }

                    offset = -CGFloat(currentIndex) * geometry.size.width
                }
            }
        }
        .ignoresSafeArea()
    }

    @ViewBuilder
    private func screenView(for screen: Screen) -> some View {
        switch screen {
        case .cameraOverlay:
            CameraOverlayView(
                navViewModel: viewModel,
                viewModel: cameraOverlayViewModel
            )
        case .ingredientsView:
            IngredientsView(navViewModel: viewModel)
        case .recipesView:
            RecipesView(navViewModel: viewModel)
        }
    }

}
