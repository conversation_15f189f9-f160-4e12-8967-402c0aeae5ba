import SwiftUI

// Import your Ingredient model if needed

class CustomNavigationViewModel: ObservableObject {
    @Published var inventory: [Ingredient] = [] // Shared inventory for IngredientsView

    // Dummy toggles for testing
    @Published var shouldShowRecipesView: Bool = true
    @Published var shouldShowIngredientsView: Bool = true
    
    // Example: You could add logic here to toggle the above properties
    // For now, hardcoded to true for testing

    init() {
        // #if DEBUG
        // // Initialize with some sample data
        // inventory = [
        //     Ingredient(item_location: "top shelf", brand: "Heinz", name: "ketchup", food_category: "condiment", dietary_attributes: nil, quantity_value: "1", unit: "bottle", container: "bottle", expiration_date: "2025-06-01", is_confident: true, note: nil),
        //     Ingredient(item_location: "fridge", brand: nil, name: "milk", food_category: "dairy", dietary_attributes: "organic", quantity_value: "2", unit: "carton", container: "carton", expiration_date: "2025-04-30", is_confident: true, note: "Uncertain: possible 1L"),
        //     Ingredient(item_location: "bottom shelf", brand: "Land O'Lakes", name: "butter", food_category: "dairy", dietary_attributes: "organic", quantity_value: "2", unit: "sticks", container: "box", expiration_date: "2025-04-30", is_confident: false, note: "Uncertain: possible 1L")
        // ]
        // #endif
    }
}
