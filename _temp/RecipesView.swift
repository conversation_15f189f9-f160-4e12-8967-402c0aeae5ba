import SwiftUI

struct RecipesView: View {
    @ObservedObject var navViewModel: CustomNavigationViewModel
    
    var body: some View {
        ZStack {
            // VisualEffectBlur(blurStyle: .systemMaterial)
            //     .clipShape(RoundedRectangle(cornerRadius: 28, style: .continuous))
            //     .shadow(radius: 12)
            VStack {
            Text("Recipes")
                .font(.largeTitle)
                .padding(.top)
        }
            .padding([.vertical, .bottom])
        }
        .clipShape(RoundedRectangle(cornerRadius: 28, style: .continuous))
        .shadow(radius: 12)
    }
}

// Dummy preview
struct RecipesView_Previews: PreviewProvider {
    static var previews: some View {
        RecipesView(navViewModel: CustomNavigationViewModel())
    }
}
