import UIKit
import SwiftUI
import PostHog

struct CameraOverlayView: View {
    @ObservedObject var navViewModel: CustomNavigationViewModel
    @ObservedObject var viewModel: CameraOverlayViewModel

    var body: some View {
        // MARK: Main View (ZStack) 
        ZStack {
            // Background for rounded corners
            // Color(.systemBackground)
            //     .cornerRadius(28)
            //     .shadow(radius: 12)
            //     .edgesIgnoringSafeArea(.all) // Optional: Remove if you want to respect safe area

            // Main camera overlay content below

            // Response loading overlay (blocks interaction)
            if viewModel.isWaitingForServerResponse {
                ResponseLoadingView()
                    .transition(.opacity)
                    .zIndex(10)
            }

            CameraPreview(session: viewModel.cameraManager.session)
                .clipShape(RoundedRectangle(cornerRadius: 28, style: .continuous))
            
            // Glass overlay
            VisualEffectBlur(blurStyle: .systemMaterial)
                .opacity(viewModel.overlayOpacity)
                .clipShape(RoundedRectangle(cornerRadius: 28, style: .continuous))
            // MARK: - Control Elements (VStack)
            VStack {
                Spacer()
                // Export button at the bottom of the VStack
                Button("Export First High-Res Image") {
                    viewModel.exportFirstHighResImage()
                }
                    .disabled(viewModel.highResImages.isEmpty)
                    .padding(.bottom, 6) 
                // Temporary debug UI: show counts above thumbnails
                HStack {
                    Text("Thumbnails: \(viewModel.thumbnails.count)")
                    Text("HighRes: \(viewModel.highResImages.count)")
                }
                    .font(.caption)
                    .foregroundColor(.white)
                    .padding(8)
                    .background(Color.black.opacity(0.65))
                    .cornerRadius(10)
                    .padding(.bottom, 6)
                if !viewModel.thumbnails.isEmpty {
                    ThumbnailRowView(viewModel: viewModel)
                }
                // MARK: AnimatedSendButton
                if !viewModel.isKeyboardVisible {
                    let allThumbnailsLoaded = viewModel.thumbnails.allSatisfy { !$0.isLoading }
                    let hasContent = !viewModel.thumbnails.isEmpty || !viewModel.messageText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
                    AnimatedSendButton(
                        isVisible: allThumbnailsLoaded && hasContent,
                        action: {
                            // Show waiting overlay
                            viewModel.isWaitingForServerResponse = true
                            // Send images and message to backend
                            viewModel.sendImagesAndMessage()
                        }
                    )
                    .frame(maxWidth: .infinity)
                }
                // MARK: Input Row (MessageBubble + CaptureButton)
                let isThumbnailLoading = viewModel.thumbnails.first?.isLoading == true
                HStack(alignment: .top, spacing: 12) {
                    MessageBubble(text: $viewModel.messageText)
                    if viewModel.spinnerLoading {
                        SpinnerCircle(isLoading: $viewModel.spinnerLoading)
                            .frame(width: 63, height: 63)
                            .transition(.opacity)
                    } else {
                        CaptureButton2(
                            isPressed: $viewModel.captureButtonPressed,
                            onTouchDown: {
                                PostHogSDK.shared.capture("Test Event")
                                // set FPS here for when user slides out of button, then back in
                                viewModel.setFPS(30)
                                print("[ContentView] onTouchDown - hiding overlay")
                                withAnimation(.easeInOut(duration: 0.15)) {
                                    viewModel.overlayOpacity = 0.0
                                }
                            },
                            onTouchUp: {
                                withAnimation(.easeInOut(duration: 0.15)) {
                                    viewModel.overlayOpacity = 1.0
                                }
                                // camera set to 15 FPS in CameraManager at the end of photoOutput()
                                viewModel.handleCapture()
                            },
                            onTouchUpCancel: {
                                print("[ContentView] onTouchUpCancel - showing overlay")
                                viewModel.setFPS(15)
                                withAnimation(.easeInOut(duration: 0.15)) {
                                    viewModel.overlayOpacity = 1.0
                                }
                            },
                            onLongPress: {
                                print("[ContentView] hold threshold reached; switching to 30 FPS")
                                viewModel.setFPS(30)
                            }
                        )
                        .scaleEffect(viewModel.captureButtonPressed ? 0.93 : 1.0)
                        .animation(.spring(response: 0.23, dampingFraction: 0.7), value: viewModel.captureButtonPressed)
                        .frame(width: 63, height: 63)
                        .transition(.scale.combined(with: .opacity))
                    }
                }
                .padding(.horizontal, 10)
                .padding(.bottom, 10)
                .padding(.top, 4)
                .onChange(of: isThumbnailLoading) { newValue in
                    viewModel.spinnerLoading = newValue
                }
            }
            .padding(.vertical)
        }
        .clipShape(RoundedRectangle(cornerRadius: 28, style: .continuous))
        .shadow(radius: 12)
        .onAppear {
            // Load persisted inventory on launch
            #if !DEBUG
            navViewModel.inventory = viewModel.loadInventoryFromUserDefaults()
            #endif
            
            viewModel.onSendSuccess = { inventory in
                navViewModel.inventory = inventory
                navViewModel.shouldShowIngredientsView = true
            }

            // show/dismiss keyboard observers
            NotificationCenter.default.addObserver(forName: UIResponder.keyboardWillShowNotification, object: nil, queue: .main) { _ in
                withAnimation(.easeIn(duration: 0.1)) {
                    viewModel.isKeyboardVisible = true
                }
            }
            NotificationCenter.default.addObserver(forName: UIResponder.keyboardWillHideNotification, object: nil, queue: .main) { _ in
                withAnimation(.spring()) {
                    viewModel.isKeyboardVisible = false
                }
            }
        }
        .onDisappear {
            // Remove keyboard observers
            NotificationCenter.default.removeObserver(self, name: UIResponder.keyboardWillShowNotification, object: nil)
            NotificationCenter.default.removeObserver(self, name: UIResponder.keyboardWillHideNotification, object: nil)
        }
        // Share sheet presentation
        .sheet(isPresented: $viewModel.showShareSheet, onDismiss: { viewModel.exportURL = nil }) {
            if let exportURL = viewModel.exportURL {
                ShareSheet(activityItems: [exportURL])
            }
        }
    }
}
