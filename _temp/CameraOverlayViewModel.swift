import SwiftUI
import Combine

class CameraOverlayViewModel: ObservableObject {
    // Closure to notify the view when sending succeeds
    var onSendSuccess: (([Ingredient]) -> Void)?
    // Vision Request Endpoint
    let visionRequestEndpoint: String = "https://bfac-2603-6013-a940-2671-fd4d-fdbf-dde2-883a.ngrok-free.app/app/ios"
    
    // MARK: - Published Properties
    @Published var thumbnails: [Thumbnail] = []
    @Published var highResImages: [UIImage] = []
    @Published var messageText: String = ""
    @Published var isKeyboardVisible: Bool = false
    @Published var spinnerLoading: Bool = false
    @Published var overlayOpacity: Double = 1.0
    @Published var captureButtonPressed: Bool = false
    @Published var showShareSheet: Bool = false
    @Published var exportURL: URL? = nil
    @Published var isWaitingForServerResponse: Bool = false
    @Published var serverError: String? = nil

    // MARK: - Camera - CameraManager
    let cameraManager = CameraManager()
    private var cancellables = Set<AnyCancellable>()

    init() {
        // Optionally observe camera state or other publishers here
    }

    private let inventoryKey = "persistedInventory"

    func loadInventoryFromUserDefaults() -> [Ingredient] {
        if let data = UserDefaults.standard.data(forKey: inventoryKey),
           let inventory = try? JSONDecoder().decode([Ingredient].self, from: data) {
            return inventory
        }
        return []
    }

    func saveInventoryToUserDefaults(_ inventory: [Ingredient]) {
        if let data = try? JSONEncoder().encode(inventory) {
            UserDefaults.standard.set(data, forKey: inventoryKey)
        }
    }

    // MARK: - Capture Photo
    func handleCapture() {
        // Insert loading placeholder first
        thumbnails.insert(Thumbnail(image: nil, isLoading: true), at: 0)
        cameraManager.capturePhoto { [weak self] image in
            guard let self = self, let image = image else { return }
            // Generate thumbnail in background
            DispatchQueue.global(qos: .userInitiated).async {
                let thumbnail = image.resizedThumbnail(maxDimension: 120)
                DispatchQueue.main.async {
                    // Replace the loading slot with the real thumbnail
                    if !self.thumbnails.isEmpty, self.thumbnails[0].isLoading {
                        self.thumbnails[0] = Thumbnail(image: thumbnail, isLoading: false)
                    }
                    self.highResImages.insert(image, at: 0)
                }
            }
        }
    }

    // MARK: Send Images and Message
    func sendImagesAndMessage() {
        // Prepare images as base64 strings
        let imagesBase64: [String] = highResImages.compactMap { image in
            guard let jpegData = image.jpegData(compressionQuality: 0.9) else { return nil }
            return jpegData.base64EncodedString()
        }
        let message = messageText
        let payload: [String: Any] = [
            "images": imagesBase64,
            "message": message
        ]
        guard let url = URL(string: visionRequestEndpoint) else {
            print("[Send] Invalid URL")
            return
        }
        guard let jsonData = try? JSONSerialization.data(withJSONObject: payload) else {
            print("[Send] Failed to serialize JSON payload")
            return
        }
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.httpBody = jsonData
        request.timeoutInterval = 300 // 5 minutes
        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            defer {
                // Always hide the loading overlay when response is handled
                DispatchQueue.main.async {
                    self.isWaitingForServerResponse = false
                }
            }
            if let error = error {
                print("[Send] Error: \(error)")
                return
            }
            guard let data = data else {
                print("[Send] No data received")
                return
            }
            if let responseString = String(data: data, encoding: .utf8) {
                print("[Send] Response: \(responseString)")
                // Try to decode inventory from response
                if let inventory = self.parseInventory(from: data) {
                    print("[DEBUG] Parsed inventory count: \(inventory.count)")
                    print("[DEBUG] Parsed inventory items: \(inventory)")
                    DispatchQueue.main.async {
                        self.saveInventoryToUserDefaults(inventory)
                        self.serverError = nil
                        // Notify the view so it can update navViewModel
                        self.onSendSuccess?(inventory)
                    }
                } else {
                    print("[DEBUG] Failed to parse inventory from data")
                    DispatchQueue.main.async {
                        self.serverError = "Failed to parse inventory from server response."
                    }
                }
            }
        }
        task.resume();
    }

    // MARK: Parse Inventory
    func parseInventory(from data: Data) -> [Ingredient]? {
        // Try to decode the expected JSON structure
        struct ServerResponse: Decodable {
            let results: [ResultItem]
            struct ResultItem: Decodable {
                let ingredients_list: InventoryWrapper?
                let error: String?
                struct InventoryWrapper: Decodable {
                    let inventory: [Ingredient]?
                }
            }
        }
        do {
            let decoded = try JSONDecoder().decode(ServerResponse.self, from: data)
            print("[DEBUG] Decoded ServerResponse: \(decoded)")
            // Flatten all inventory arrays from results
            let allItems = decoded.results.compactMap { $0.ingredients_list?.inventory }.flatMap { $0 }
            print("[DEBUG] Flattened inventory items: \(allItems)")
            return allItems.isEmpty ? nil : allItems
        } catch {
            print("[ParseInventory] Error: \(error)")
            return nil
        }
    }

     // MARK: - Export Image via Share Sheet
    func exportFirstHighResImage() {
        guard let image = highResImages.first,
              let data = image.jpegData(compressionQuality: 0.9) else { return }
        let tempDir = FileManager.default.temporaryDirectory
        let url = tempDir.appendingPathComponent("groceries.jpg")
        do {
            try data.write(to: url)
            exportURL = url
            showShareSheet = true
        } catch {
            print("[CameraOverlayViewModel] [Export Error] Failed to write image for export: \(error)")
        }
    }

    func removeThumbnail(at index: Int) {
        thumbnails.remove(at: index)
        highResImages.remove(at: index)
    }

    func setFPS(_ fps: Double) {
        cameraManager.setFPS(fps)
    }
}
