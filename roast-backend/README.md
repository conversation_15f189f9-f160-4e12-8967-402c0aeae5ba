# backend for the roast app

middleware for gathering roast client data and relaying image and text data to a vision-capable LLM.

working to flesh out the data preprocessing pipeline to improve accuracy, costs, and performance.

<PERSON> will leverage vLLMs heavily, then computer vision and machine learning techniques need to be applied.

## Implementing LLM with OpenAI-spec APIs

OpenAI has moved away from the Chat Completions API and are now using the Responses API, which has extra features that may need to be hardcoded if using the Chat Completions API. Refer to the API docs for details on client setup.

Structured Outputs are different:

**Chat Completions API**

```python
format_schema = {
    "type": "json_schema",
    "json_schema": {
        "name": "ingredients_list",
        "strict": True,
        "schema": {
            "type": "object",
            "properties": {
                "ingredients_list": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "name": {
                                "type": "string",
                                "description": "The normalized, canonical name of the grocery item"
                            },
                            "quantity": {
                                "type": "string",
                                "description": "The numeric quantity of the item detected. Use null if the quantity is unknown or uncertain."
                            },
                            "unit": {
                                "type": "string",
                                "description": "The standardized unit of measurement for the quantity. Use null if unknown."
                            },
                            "category": {
                                "type": "string",
                                "description": "The category or type of the grocery item to aid organization and meal planning"
                            },
                            "is_confident": {
                                "type": "boolean",
                                "description": "Indicates whether the item identification and quantity are confident or uncertain (true/false)"
                            }
                        },
                        "required": ["name", "quantity", "unit", "category", "is_confident"],
                        "additionalProperties": False
                    }
                },
                "required": ["ingredients_list"],
                "additionalProperties": False
            }
        }
    }
}
```

**Responses API**

```python
format_schema = {
    "format" : {
        "type": "json_schema",
        "name": "ingredients_list",
        "schema": {
            "type": "object",
            "properties": {
                "ingredients": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "item_name": {"type": "string"},
                            "quantity": {"type": "string"},
                            "quantity_unit_type": {"type": "string"},
                            "accuracy": {"type": "boolean"}
                        },
                        "required": ["item_name", "quantity", "quantity_unit_type", "accuracy"],
                        "additionalProperties": False,
                    }
                },
                "required": ["ingredients"],
                "additionalProperties": False,
            },
            "strict": True
        },
    }
}
```