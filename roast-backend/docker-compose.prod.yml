services:
  roast:
    container_name: roast_prod
    build: .
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - roast_net
    env_file:
      - .env.prod
    ports:
      - "8000:8000"
    restart: unless-stopped
    environment:
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1
      - DEBUG=0
    command: ["uv", "run", "main.py", "--workers", "4"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/app"]
      interval: 15s
      timeout: 5s
      retries: 5
    logging:
      driver: json-file
      options:
        max-size: "20m"
        max-file: "5"
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 1G

  redis:
    restart: unless-stopped
    healthcheck:
      interval: 5s
