services:
  roast_dev:
    container_name: roast_dev
    build: .
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - roast_net
    env_file:
      - .env.dev
    ports:
      - "8001:8000"
    volumes:
      - .:/app # live code reload?
    environment:
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1
      - DEBUG=1
    command: ["uv", "run", "main.py", "--reload"]
    healthcheck:
      interval: 30s
      timeout: 10s
    logging:
      options:
        max-size: "5m"
        max-file: "1"
