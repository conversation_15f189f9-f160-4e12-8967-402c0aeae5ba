import base64
import json
import math
import os
import time
import uuid
from contextlib import asynccontextmanager

import asyncio
import redis
from dotenv import load_dotenv
from fastapi import FastAPI, Form, Request, status, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from fastapi.encoders import jsonable_encoder
from openai import OpenAI
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request
from aioapns import APNs, NotificationRequest, PushType
# from starlette.responses import Response

from models import RecipeSeedRequest, IngredientRequest, RecipeRequest, PushTokenRequest
from prompts import sp_ingredients, sp_ingredients_dev, sp_recipes, sp_recipe

# get .env vars
_didEnvLoad = load_dotenv()

OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY")
REDIS_HOST = os.getenv("REDIS_HOST", "localhost")
REDIS_PORT = int(os.getenv("REDIS_PORT", 6379))

# APNS configuration
APNS_KEY_PATH = os.getenv("APNS_KEY_PATH")
APNS_KEY_ID = os.getenv("APNS_KEY_ID")
APNS_TEAM_ID = os.getenv("APNS_TEAM_ID")
APNS_BUNDLE_ID = os.getenv("APNS_BUNDLE_ID")
APNS_USE_SANDBOX = os.getenv("APNS_USE_SANDBOX", "true").lower() == "true"

# Define a dev session ID for bypassing rate limits
DEV_SESSION_ID = os.getenv("DEV_SESSION_ID")

redis_client = redis.StrictRedis(host=REDIS_HOST, port=REDIS_PORT, db=0)

# Initialize APNS client
apns_client = None
if APNS_KEY_PATH and APNS_KEY_ID and APNS_TEAM_ID and APNS_BUNDLE_ID:
    try:
        with open(APNS_KEY_PATH, "r") as key_file:
            apns_key = key_file.read()
        apns_client = APNs(
            key=apns_key,
            key_id=APNS_KEY_ID,
            team_id=APNS_TEAM_ID,
            topic=APNS_BUNDLE_ID,
            use_sandbox=APNS_USE_SANDBOX,
        )
        print(
            f"APNS client initialized for {APNS_BUNDLE_ID} (sandbox: {APNS_USE_SANDBOX})"
        )
    except Exception as e:
        print(f"Failed to initialize APNS client: {e}")
        apns_client = None
else:
    print("APNS configuration not complete. Push notifications disabled.")

# define rate limits per endpoint
# "window_seconds" defines the size of the fixed windows used for approximation
RATE_LIMIT_CONFIG = {
    # 3 req within 1 month window
    "/api/ingredients": {
        "limit": 3,
        "window_seconds": 2629746,
    },
    # 14 req within 1 month window
    "/api/recipes": {
        "limit": 14,
        "window_seconds": 2629746,
    },
    # 14 req within 1 month window
    "/api/recipe": {
        "limit": 14,
        "window_seconds": 2629746,
    },
    # 10 req within 24 hour window
    None: {"limit": 10, "window_seconds": 86400},
}


# MIDDLEWARE - rate limit management
class RateLimitMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        endpoint_path = request.url.path
        if endpoint_path == "/app" or endpoint_path == "/session":
            return await call_next(request)

        # get session id from request state (set by SessionMiddleware)
        session_id = request.state.session_id

        # get the path of the requested endpoint
        endpoint_path = request.url.path

        # handle requests without a session id
        if session_id is None:
            # option 1: allow requests without a session id to bypass rate limiting.
            # return await call_next(request)
            # option 2: return an error for requests without a session ID.
            return JSONResponse(
                status_code=401, content={"detail": "session ID required."}
            )

        rate_limit = RATE_LIMIT_CONFIG.get(endpoint_path)
        if rate_limit is None:
            # default to no limit
            rate_limit = RATE_LIMIT_CONFIG.get(
                None, {"limit": float("inf"), "window_seconds": 0}
            )

        limit = rate_limit["limit"]
        window_seconds = rate_limit["window_seconds"]

        # if the limit is infinite or window is not positive, disable rate limiting
        if limit == float("inf") or window_seconds <= 0:
            return await call_next(request)

        # Allow developer to bypass rate limiting for /api/ingredients
        # when using their DEV_SESSION_ID
        if (
            DEV_SESSION_ID is not None
            and session_id == DEV_SESSION_ID
            and endpoint_path == "/api/ingredients"
        ):
            print(f"Rate limit bypass activated for dev session {session_id}")
            return await call_next(request)

        try:
            current_time = time.time()
            current_window_start = (
                math.floor(current_time / window_seconds) * window_seconds
            )
            previous_window_start = current_window_start - window_seconds

            # define unique redis keys for the counters
            current_window_key = (
                f"rate_limit:{session_id}:{endpoint_path}:{int(current_window_start)}"
            )
            previous_window_key = (
                f"rate_limit:{session_id}:{endpoint_path}:{int(previous_window_start)}"
            )

            # use a redis pipeline to get the counts for both keys atomically
            pipe = redis_client.pipeline()
            pipe.get(current_window_key)
            pipe.get(previous_window_key)
            get_results = pipe.execute()

            # retrieve counts from results
            # if a key doesnt exist, treat count as 0.
            current_window_count_bytes = get_results[0]
            previous_window_count_bytes = get_results[1]

            current_window_count = (
                int(current_window_count_bytes.decode("utf-8"))
                if current_window_count_bytes
                else 0
            )
            previous_window_count = (
                int(previous_window_count_bytes.decode("utf-8"))
                if previous_window_count_bytes
                else 0
            )

            # calculate the time elapsed within the current fixed window
            time_since_current_window_start = current_time - current_window_start

            # calculate overlap percentage of previous fixed window with sliding window
            overlap_duration = window_seconds - time_since_current_window_start
            overlap_percentage = max(
                0.0, overlap_duration / window_seconds
            )  # use 0.0 to prevent neg %

            # calculate approximated total count in the sliding window
            approximated_count = (
                previous_window_count * overlap_percentage
            ) + current_window_count

            # check if approximated count exceeds the allowed limit
            if approximated_count > limit:
                # return 429 Too Many Requests
                return JSONResponse(
                    status_code=429,
                    content={
                        "detail": f"Rate limit exceeded for endpoint {endpoint_path}. Limit: {limit} requests per {window_seconds} seconds (approximated sliding window)."
                    },
                )

            # if rate limit is not exceeded
            pipe = redis_client.pipeline()
            pipe.incr(current_window_key)
            # set expiration for key, good practice:
            # 2 * window_seconds + buffer
            pipe.expire(current_window_key, int(2 * window_seconds + 5))
            pipe.execute()
        except Exception as e:
            print(f"rate limiting redis error (sliding window): {e}")
            # decide how to handle redis errors in production (allow or return error)
            return await call_next(request)

        # if rate limit not exceeded, allow request
        response = await call_next(request)
        return response


# MIDDLEWARE - session management
class SessionMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        endpoint_path = request.url.path
        if endpoint_path == "/app" or endpoint_path == "/session":
            return await call_next(request)

        # get session id from a custom header first (sent by ios app)
        session_id = request.headers.get("x-session-id")
        # session_id = request.cookies.get("session_id") # for web

        if not session_id:
            return JSONResponse(
                status_code=401, content={"detail": "x-session-id header required."}
            )

        # get session data from redis
        session_data_json = redis_client.get(session_id)

        if not session_data_json:
            return JSONResponse(
                status_code=401, content={"detail": "Invalid or expired session ID."}
            )

        try:
            session_data = json.loads(session_data_json)
        except json.JSONDecodeError:
            return JSONResponse(
                status_code=500, content={"detail": "Corrupted session data."}
            )

        # make session data available in request state
        request.state.session_id = session_id
        request.state.session = session_data

        # process the request with the next middleware or endpoint
        response = await call_next(request)

        # save session data back to redis after the request is processed
        # redis_client.set(session_id, json.dumps(request.state.session))
        # refresh expiration to 30 days from now
        redis_client.setex(
            session_id, 30 * 24 * 60 * 60, json.dumps(request.state.session)
        )

        # for web
        # set the session cookie in the response
        # httponly=True prevents client-side JS access (security)
        # expires sets the cookie expiration (in seconds - 2 days here)
        # response.set_cookie(
        #     key="session_id", value=session_id, httponly=True, expires=172800
        # )

        return response


# app lifecycle event actions
@asynccontextmanager
async def lifespan(_app: FastAPI):
    # things to do on startup
    yield
    # things to do on shutdown
    redis_client.close()


app = FastAPI(lifespan=lifespan)


# Allow all CORS (customize origins as needed)
app.add_middleware(RateLimitMiddleware)
app.add_middleware(SessionMiddleware)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Background task to process ingredients
async def process_ingredients_background(request: IngredientRequest, session_id: str, request_id: str, session_data: dict):
    """Process ingredients in the background and store results"""
    client = OpenAI(base_url="https://openrouter.ai/api/v1", api_key=OPENROUTER_API_KEY)
    
    try:
        system_prompt = sp_ingredients
        ingredients_schema = {
            "type": "json_schema",
            "json_schema": {
                "name": "inventory",
                "strict": True,
                "schema": {
                    "type": "object",
                    "properties": {
                        "inventory": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "item_location": {"type": "string"},
                                    "brand": {"type": "string"},
                                    "name": {"type": "string"},
                                    "food_category": {"type": "string"},
                                    "dietary_attributes": {"type": "string"},
                                    "quantity_value": {"type": "number"},
                                    "unit": {"type": "string"},
                                    "container": {"type": "string"},
                                    "expiration_date": {"type": "string"},
                                    "is_confident": {"type": "boolean"},
                                    "note": {"type": "string"},
                                },
                                "required": [
                                    "item_location",
                                    "name",
                                    "food_category",
                                    "quantity_value",
                                    "unit",
                                    "container",
                                    "is_confident",
                                ],
                                "additionalProperties": False,
                            },
                        }
                    },
                    "required": ["inventory"],
                    "additionalProperties": False,
                },
            },
        }

        results = []
        for base64_image in request.images:
            try:
                user_content = []
                if request.message:
                    user_content.append({"type": "text", "text": request.message})
                user_content.append(
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{base64_image}",
                        },
                    }
                )
                response = client.chat.completions.create(
                    model="google/gemini-2.5-pro-preview-03-25",
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": user_content},
                    ],
                    response_format=ingredients_schema,
                    provider={"sort": "latency"},
                )
                ingredients_list = json.loads(response.choices[0].message.content)

                OPTIONAL_FIELDS = [
                    "brand",
                    "dietary_attributes",
                    "expiration_date",
                    "note",
                ]

                def unknown_to_none(ingredient):
                    for field in OPTIONAL_FIELDS:
                        if (
                            field in ingredient
                            and isinstance(ingredient[field], str)
                            and ingredient[field].strip().lower() == "unknown"
                        ):
                            ingredient[field] = None
                    return ingredient

                if (
                    isinstance(ingredients_list, dict)
                    and "inventory" in ingredients_list
                ):
                    ingredients_list["inventory"] = [
                        unknown_to_none(item) for item in ingredients_list["inventory"]
                    ]
                    ingredients_list["inventory"] = [
                        {**item, "id": str(uuid.uuid4())}
                        for item in ingredients_list["inventory"]
                    ]

                print(json.dumps(ingredients_list, indent=4, ensure_ascii=False))
                results.append({"ingredients_list": ingredients_list})
            except Exception as e:
                print(f"Error processing image: {e}")
                results.append({"error": str(e)})
        
        # Store results in Redis with TTL of 1 hour
        ingredient_data = {"results": results}
        redis_key = f"ingredient_data:{session_id}:{request_id}"
        redis_client.setex(redis_key, 3600, json.dumps(ingredient_data))
        print(f"Ingredient data stored in Redis for request_id: {request_id}")
        
        # Send push notification if push token is available
        push_token = session_data.get("push_token")
        if push_token and apns_client:
            await send_push_notification(push_token, request_id)
        else:
            print(f"No push token available or APNS client not initialized for session {session_id}")
    
    except Exception as e:
        print(f"Error in background processing: {e}")
        # Store error in Redis so client can retrieve it
        error_data = {"results": [{"error": str(e)}]}
        redis_key = f"ingredient_data:{session_id}:{request_id}"
        redis_client.setex(redis_key, 3600, json.dumps(error_data))


# Helper function to send push notifications
async def send_push_notification(device_token: str, request_id: str):
    """Send a push notification to the device with the given token"""
    if not apns_client or not device_token:
        print("APNS client not available or no device token provided")
        return False

    try:
        notification_request = NotificationRequest(
            device_token=device_token,
            message={
                "aps": {
                    "alert": "Your ingredient analysis is ready!",
                    "badge": 1,
                    "sound": "default",
                },
                "request_id": request_id,
            },
            notification_id=str(uuid.uuid4()),
            time_to_live=3600,  # 1 hour
            push_type=PushType.ALERT,
        )

        await apns_client.send_notification(notification_request)
        print(f"Push notification sent successfully for request_id: {request_id}")
        return True
    except Exception as e:
        print(f"Failed to send push notification: {e}")
        return False


# Add custom validation error handler
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=jsonable_encoder({"detail": exc.errors(), "body": exc.body}),
    )


# Health check endpoint
@app.get("/app")
def hq():
    return {"message": "app backend is running"}


@app.post("/api/ingredients")
async def ingredients(request: IngredientRequest, fastapi_request: Request):
    # access session data from request state
    session_id = fastapi_request.state.session_id
    session_data = fastapi_request.state.session
    print(f"session id: {session_id}")
    print(f"current session data: {session_data}")

    # Generate a unique request ID
    request_id = str(uuid.uuid4())
    
    # Start background task to process ingredients
    asyncio.create_task(process_ingredients_background(request, session_id, request_id, session_data.copy()))
    
    # Return request_id immediately
    return {"request_id": request_id}


@app.get("/api/ingredients/{request_id}")
async def get_ingredient_data(request_id: str, fastapi_request: Request):
    """Retrieve and delete ingredient data by request_id"""
    # Get session_id from request state
    session_id = fastapi_request.state.session_id

    # Construct the Redis key
    redis_key = f"ingredient_data:{session_id}:{request_id}"

    try:
        # Get the data from Redis
        ingredient_data_json = redis_client.get(redis_key)

        if not ingredient_data_json:
            raise HTTPException(
                status_code=404, detail="Ingredient data not found or expired"
            )

        # Parse the JSON data
        ingredient_data = json.loads(ingredient_data_json)

        # Delete the data from Redis
        redis_client.delete(redis_key)

        return ingredient_data
    except json.JSONDecodeError:
        raise HTTPException(status_code=500, detail="Corrupted ingredient data")
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to retrieve ingredient data: {str(e)}"
        )
    

@app.get("/api/ingredients/{request_id}/status")
async def get_ingredient_status(request_id: str, fastapi_request: Request):
    """Check if ingredient data is ready for the given request_id"""
    # Get session_id from request state
    session_id = fastapi_request.state.session_id

    # Construct the Redis key
    redis_key = f"ingredient_data:{session_id}:{request_id}"

    try:
        # Check if the data exists in Redis
        exists = redis_client.exists(redis_key)

        if exists:
            return {"status": "ready", "request_id": request_id}
        else:
            return {"status": "processing", "request_id": request_id}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to check status: {str(e)}")


@app.post("/api/recipes")
async def recipes(request: RecipeSeedRequest):
    client = OpenAI(base_url="https://openrouter.ai/api/v1", api_key=OPENROUTER_API_KEY)
    try:
        system_prompt = sp_recipes
        recipes_schema = {
            "type": "json_schema",
            "json_schema": {
                "name": "recipes",
                "strict": True,
                "schema": {
                    "type": "object",
                    "properties": {
                        "recipes": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "name": {"type": "string"},
                                    "origin": {"type": "string"},
                                    "description": {"type": "string"},
                                    "duration": {"type": "string"},
                                    "difficulty": {"type": "string"},
                                    "instructions": {
                                        "type": "array",
                                        "items": {"type": "string"},
                                    },
                                    "flavor_profile": {"type": "string"},
                                    "key_tags": {
                                        "type": "array",
                                        "items": {"type": "string"},
                                    },
                                    "ingredients": {
                                        "type": "array",
                                        "items": {"type": "string"},
                                    },
                                },
                                "required": [
                                    "name",
                                    "origin",
                                    "description",
                                    "duration",
                                    "difficulty",
                                    "instructions",
                                    "flavor_profile",
                                    "key_tags",
                                    "ingredients",
                                ],
                                "additionalProperties": False,
                            },
                        }
                    },
                    "required": ["recipes"],
                    "additionalProperties": False,
                },
            },
        }

        user_content = "Please recommend 3 recipes that I can cook from the list of ingredients provided."

        user_content += f"\nIngredients: {request.ingredients}"

        response = client.chat.completions.create(
            # model="openai/o4-mini-high",
            model="google/gemini-2.5-flash-preview",
            # model="google/gemini-2.5-pro-preview-03-25",
            # model="qwen/qwen3-235b-a22b:free",
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_content},
            ],
            response_format=recipes_schema,
        )
        print("Raw response:", response)
        # Defensive checks
        if not response or not hasattr(response, "choices") or not response.choices:
            return JSONResponse(
                status_code=500,
                content={
                    "error": "No choices returned from model",
                    "raw_response": str(response),
                },
            )
        if not hasattr(response.choices[0], "message") or not hasattr(
            response.choices[0].message, "content"
        ):
            return JSONResponse(
                status_code=500,
                content={
                    "error": "No content in model response",
                    "raw_response": str(response),
                },
            )
        recipes_str = response.choices[0].message.content
        print("recipes_str:", recipes_str)
        recipes_json = json.loads(recipes_str)

        if isinstance(recipes_json, dict) and "recipes" in recipes_json:
            recipes_json["recipes"] = [
                {**item, "id": str(uuid.uuid4())} for item in recipes_json["recipes"]
            ]
            return {"recipes": recipes_json["recipes"]}
        else:
            recipes_json = [{**item, "id": str(uuid.uuid4())} for item in recipes_json]
            return {"recipes": recipes_json}
    except Exception as e:
        return JSONResponse(status_code=500, content={"error": str(e)})


@app.post("/api/recipe")
async def recipe(request: RecipeRequest):
    client = OpenAI(base_url="https://openrouter.ai/api/v1", api_key=OPENROUTER_API_KEY)

    recipe_seed = request.recipe_seed
    available_ingredients = "\n".join(
        f"- {line}" for line in request.available_ingredients
    )
    message = ""

    if request.message:
        message = f"Here are some notes I may have about my diet or preferences: {request.message}"

    system_prompt = sp_recipe

    recipe_schema = {
        "type": "json_schema",
        "json_schema": {
            "name": "recipe",
            "description": "An helpful guide to cook a dish.",
            "strict": True,
            "schema": {
                "type": "object",
                "required": [
                    "name",
                    "cook_time",
                    "servings",
                    "ingredients",
                    "instructions",
                ],
                "additionalProperties": False,
                "properties": {
                    "name": {"type": "string"},
                    "prep_time": {"type": "string"},
                    "cook_time": {"type": "string"},
                    "servings": {"type": "integer"},
                    "ingredients": {
                        "type": "array",
                        "items": {"type": "string"},
                    },
                    "instructions": {
                        "type": "array",
                        "items": {"type": "string"},
                    },
                },
            },
        },
    }

    user_content = f"""
    I was recommended a recipe with the following parameters:
    Name: {recipe_seed.name}
    Description: {recipe_seed.description}
    Origin: {recipe_seed.origin}
    Total Expected Prep and Cook Time: {recipe_seed.duration}
    Difficulty: {recipe_seed.difficulty}
    Instructions Outline: {recipe_seed.instructions}
    Flavor profile: {recipe_seed.flavor_profile}
    Key tags: {recipe_seed.key_tags}
    Expected Ingredients: {recipe_seed.ingredients}

    ---
    Can you provide a recipe that utilizes this information to create a complete, robust, accurate, and helpful, simple recipe?

    Here is a list of my actual ingredients. Make sure I have all the ingredients necessary for this recipe, and note any replacements or substitutions in the instructions:
    {available_ingredients}

    {message}
    """

    try:
        response = client.chat.completions.create(
            # model="openai/o4-mini-high",
            model="google/gemini-2.5-flash-preview-05-20",
            messages=[
                {
                    "role": "system",
                    "content": system_prompt,
                },
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": user_content,
                        },
                    ],
                },
            ],
            response_format=recipe_schema,
        )

        print("Raw model output:", response.choices[0].message.content)

        recipe_json = json.loads(response.choices[0].message.content)
        if isinstance(recipe_json, dict):
            unique_id = str(uuid.uuid4())
            recipe_json["id"] = unique_id
            recipe_json["recipeSeedId"] = recipe_seed.id
            return {"recipe": recipe_json}
        else:
            return JSONResponse(
                status_code=500, content={"error": "Invalid recipe format"}
            )
    except Exception as e:
        return JSONResponse(status_code=500, content={"error": str(e)})


@app.post("/dev/ingredients")
async def extract_ingredients(
    image_path: str = Form(...),
    # text: str = Form(...)
):
    image_path_expanded = os.path.expanduser(image_path)
    if not os.path.isfile(image_path_expanded):
        return JSONResponse(
            status_code=400,
            content={"error": f"Image file not found at {image_path_expanded}"},
        )

    def encode_image(image_path):
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode("utf-8")

    base64_image = encode_image(image_path_expanded)

    client = OpenAI(base_url="https://openrouter.ai/api/v1", api_key=OPENROUTER_API_KEY)

    system_prompt = sp_ingredients_dev

    ingredient_schema = {
        "type": "json_schema",
        "json_schema": {
            "name": "inventory",
            "strict": True,
            "schema": {
                "type": "object",
                "properties": {
                    "inventory": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "item_location": {"type": "string"},
                                "brand": {"type": ["string", "null"]},
                                "name": {"type": "string"},
                                "food_category": {"type": "string"},
                                "dietary_attributes": {"type": ["string", "null"]},
                                "quantity_value": {"type": "string"},
                                "unit": {
                                    "type": "string",
                                    "enum": [
                                        "gallon",
                                        "liter",
                                        "milliliter",
                                        "ounce",
                                        "gram",
                                        "pound",
                                        "kilogram",
                                        "can",
                                        "unit",
                                    ],
                                },
                                "container": {
                                    "type": "string",
                                    "enum": [
                                        "can",
                                        "jar",
                                        "bottle",
                                        "bag",
                                        "carton",
                                        "wrap",
                                        "loose",
                                    ],
                                },
                                "expiration_date": {"type": ["string", "null"]},
                                "is_confident": {"type": "boolean"},
                                "note": {"type": ["string", "null"]},
                            },
                            "required": [
                                "item_location",
                                "brand",
                                "name",
                                "food_category",
                                "dietary_attributes",
                                "quantity_value",
                                "unit",
                                "container",
                                "expiration_date",
                                "is_confident",
                                "note",
                            ],
                            "additionalProperties": False,
                        },
                    }
                },
                "required": ["inventory"],
                "additionalProperties": False,
            },
        },
    }

    try:
        response = client.chat.completions.create(
            model="openai/o4-mini-high",
            messages=[
                {
                    "role": "system",
                    "content": system_prompt,
                },
                {
                    "role": "user",
                    "content": [
                        #     {
                        #         "type": "text",
                        #         "text": text
                        #     },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}",
                            },
                        }
                    ],
                },
            ],
            response_format=ingredient_schema,
        )
        # logging.warning(f"OpenAI API response: {response}")

        ingredients_list = json.loads(response.choices[0].message.content)
    except Exception as e:
        return JSONResponse(status_code=500, content={"error": str(e)})

    return {"image_path": image_path, "ingredients_list": ingredients_list}


@app.get("/session")
async def create_session():
    """
    Creates a new session in redis and returns the session id.
    The client (iOS app) should call this first to get a session id.
    """
    session_id = str(uuid.uuid4())
    session_data = {}

    try:
        # store session data
        # redis_client.set(session_id, json.dumps(session_data))
        redis_client.setex(session_id, 30 * 24 * 60 * 60, json.dumps(session_data))
        print("session ID created in redis")
        return {"session_id": session_id}
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"error": f"Failed to create session: {str(e)}"},
        )


@app.post("/session")
async def update_session_push_token(
    request: PushTokenRequest, fastapi_request: Request
):
    """Update session with push token"""
    try:
        push_token = request.push_token

        # Get session_id from header directly (since middleware is bypassed)
        session_id = fastapi_request.headers.get("x-session-id")

        if not session_id:
            return JSONResponse(
                status_code=401, content={"error": "session id required"}
            )

        # Get existing session data from Redis
        session_data_json = redis_client.get(session_id)

        if not session_data_json:
            return JSONResponse(
                status_code=401, content={"error": "Invalid or expired session ID"}
            )

        try:
            session_data = json.loads(session_data_json)
        except json.JSONDecodeError:
            return JSONResponse(
                status_code=500, content={"error": "Corrupted session data"}
            )

        # Update session data with push token
        session_data["push_token"] = push_token

        # Save updated session data back to Redis
        redis_client.setex(session_id, 30 * 24 * 60 * 60, json.dumps(session_data))

        print(f"Push token updated for session {session_id}")
        return {"message": "Push token updated successfully"}
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"error": f"Failed to update session: {str(e)}"},
        )


@app.get("/api/test-redis")
async def test_redis():
    try:
        # set a key
        redis_client.set("test_key", "redis-test.")
        # get the key
        value = redis_client.get("test_key")
        # delete the key
        redis_client.delete("test_key")
        return {
            "message": f"successfully connected to redis. got value: {value.decode('utf-8')}"
        }
    except Exception as e:
        return JSONResponse(
            status_code=500, content={"error": f"could not connect to Redis: {str(e)}"}
        )


if __name__ == "__main__":
    import uvicorn

    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
