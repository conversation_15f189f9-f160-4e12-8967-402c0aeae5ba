[project]
name = "roast-backend"
version = "0.1.0"
description = "backend for the roast app"
readme = "README.md"
author = "posteego <<EMAIL>>"
requires-python = ">=3.8"
dependencies = [
    "fastapi",
    "uvicorn",
    "python-dotenv",
    "openai",
    "python-multipart>=0.0.20",
    "pydantic>=2.10.6",
    "redis>=6.0.0",
    "aioapns>=4.0",
]

[tool.uv]
# Optional: You can specify scripts or other uv-specific settings here
