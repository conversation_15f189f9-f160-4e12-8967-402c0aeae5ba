from pydantic import BaseModel, HttpUrl


class IngredientRequest(BaseModel):
    images: list[str]
    message: str | None = None


class PushTokenRequest(BaseModel):
    push_token: str


class RecipeSeedRequest(BaseModel):
    ingredients: str
    message: str | None = None


# for validation, maybe
class RecipeSeed(BaseModel):
    id: str
    name: str
    description: str
    origin: str
    duration: str
    difficulty: str
    instructions: list[str]
    flavor_profile: str
    key_tags: list[str]
    ingredients: list[str]


class RecipeRequest(BaseModel):
    recipe_seed: RecipeSeed
    available_ingredients: list[str]
    message: str | None = None


class Recipe(BaseModel):
    name: str
    prep_time: str | None = None
    cook_time: str
    servings: int
    ingredients: list[str]
    instructions: list[str]


### SCHEMA.ORG RECIPE TYPE ###
class NutritionInformation(BaseModel):
    calories: str | None = None
    carbohydrateContent: str | None = None
    cholesterolContent: str | None = None
    fatContent: str | None = None
    fiberContent: str | None = None
    proteinContent: str | None = None
    saturatedFatContent: str | None = None
    servingSize: str | None = None
    sodiumContent: str | None = None
    sugarContent: str | None = None
    transFatContent: str | None = None
    unsaturatedFatContent: str | None = None


class RecipeSchema(BaseModel):
    name: str
    image: HttpUrl | list[HttpUrl] | str | list[str]
    description: str | None
    author: str | None
    datePublished: str | None
    prepTime: str | None  # ISO 8601 duration
    cookTime: str | None  # ISO 8601 duration
    totalTime: str | None  # ISO 8601 duration
    cookingMethod: str  # frying, steaming, etc
    nutrition: NutritionInformation | None
    recipeCategory: str | None
    recipeCuisine: str | None
    recipeIngredient: str | None
    recipeInstructions: str | None
    recipeYield: str | None
    suitableForDiet: str | None  # diet: diabetic, halal, etc. enum.
